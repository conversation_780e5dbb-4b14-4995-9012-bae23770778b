"""
Malware signature database management for the antivirus endpoint agent.
"""

import json
import os
import hashlib
import sqlite3
from datetime import datetime
from pathlib import Path

class MalwareDatabase:
    """Manages malware signatures and whitelist database."""
    
    def __init__(self, db_file="data/malware_signatures.json", whitelist_file="data/whitelist.json"):
        self.db_file = db_file
        self.whitelist_file = whitelist_file
        
        # Create data directory
        os.makedirs("data", exist_ok=True)
        
        # Initialize databases
        self.signatures = self.load_signatures()
        self.whitelist = self.load_whitelist()
    
    def load_signatures(self):
        """Load malware signatures from file."""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r') as f:
                    return json.load(f)
            else:
                # Create default signature database
                default_signatures = self.create_default_signatures()
                self.save_signatures(default_signatures)
                return default_signatures
        except Exception as e:
            print(f"Error loading signatures: {e}")
            return {"signatures": [], "last_updated": datetime.now().isoformat()}
    
    def create_default_signatures(self):
        """Create a default malware signature database with known malware hashes."""
        return {
            "signatures": [
                {
                    "hash_md5": "5d41402abc4b2a76b9719d911017c592",
                    "hash_sha256": "2c26b46b68ffc68ff99b453c1d30413413422d706483bfa0f98a5e886266e7ae",
                    "name": "Test.Malware.A",
                    "type": "trojan",
                    "severity": "high",
                    "description": "Test malware signature",
                    "added_date": "2024-01-01"
                },
                {
                    "hash_md5": "098f6bcd4621d373cade4e832627b4f6",
                    "hash_sha256": "ef2d127de37b942baad06145e54b0c619a1f22327b2ebbcfbec78f5564afe39d",
                    "name": "Test.Malware.B",
                    "type": "virus",
                    "severity": "medium",
                    "description": "Another test malware signature",
                    "added_date": "2024-01-01"
                },
                # Known malware families (example hashes - replace with real ones)
                {
                    "hash_sha256": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
                    "name": "WannaCry.Variant",
                    "type": "ransomware",
                    "severity": "critical",
                    "description": "WannaCry ransomware variant",
                    "added_date": "2024-01-01"
                },
                {
                    "hash_sha256": "b3a8e0e1f9ab1bfe3a36f231f676f78bb30a519d2b21e6c530c0eee8ebb4a5d0",
                    "name": "Emotet.Banking",
                    "type": "banking_trojan",
                    "severity": "high",
                    "description": "Emotet banking trojan",
                    "added_date": "2024-01-01"
                }
            ],
            "last_updated": datetime.now().isoformat(),
            "version": "1.0"
        }
    
    def load_whitelist(self):
        """Load whitelist from file."""
        try:
            if os.path.exists(self.whitelist_file):
                with open(self.whitelist_file, 'r') as f:
                    return json.load(f)
            else:
                # Create default whitelist
                default_whitelist = self.create_default_whitelist()
                self.save_whitelist(default_whitelist)
                return default_whitelist
        except Exception as e:
            print(f"Error loading whitelist: {e}")
            return {"hashes": [], "paths": [], "processes": []}
    
    def create_default_whitelist(self):
        """Create default whitelist with common system files."""
        return {
            "hashes": [
                # Common Windows system file hashes (examples)
                "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",  # Empty file
            ],
            "paths": [
                "C:\\Windows\\System32\\",
                "C:\\Windows\\SysWOW64\\",
                "C:\\Program Files\\Windows Defender\\",
                "C:\\Program Files (x86)\\Microsoft\\",
                "C:\\Program Files\\Microsoft\\"
            ],
            "processes": [
                "explorer.exe",
                "winlogon.exe",
                "csrss.exe",
                "smss.exe",
                "services.exe"
            ],
            "last_updated": datetime.now().isoformat()
        }
    
    def save_signatures(self, signatures=None):
        """Save signatures to file."""
        if signatures is None:
            signatures = self.signatures
        
        try:
            with open(self.db_file, 'w') as f:
                json.dump(signatures, f, indent=4)
        except Exception as e:
            print(f"Error saving signatures: {e}")
    
    def save_whitelist(self, whitelist=None):
        """Save whitelist to file."""
        if whitelist is None:
            whitelist = self.whitelist
        
        try:
            with open(self.whitelist_file, 'w') as f:
                json.dump(whitelist, f, indent=4)
        except Exception as e:
            print(f"Error saving whitelist: {e}")
    
    def is_malware(self, file_hash, hash_type="sha256"):
        """Check if a hash matches known malware."""
        hash_key = f"hash_{hash_type}"
        
        for signature in self.signatures.get("signatures", []):
            if signature.get(hash_key) == file_hash:
                return True, signature
        
        return False, None
    
    def is_whitelisted(self, file_path, file_hash=None):
        """Check if a file is whitelisted."""
        file_path = str(file_path).lower()
        
        # Check path whitelist
        for whitelisted_path in self.whitelist.get("paths", []):
            if file_path.startswith(whitelisted_path.lower()):
                return True
        
        # Check hash whitelist
        if file_hash:
            if file_hash in self.whitelist.get("hashes", []):
                return True
        
        return False
    
    def add_signature(self, hash_md5, hash_sha256, name, malware_type, severity, description):
        """Add a new malware signature."""
        new_signature = {
            "hash_md5": hash_md5,
            "hash_sha256": hash_sha256,
            "name": name,
            "type": malware_type,
            "severity": severity,
            "description": description,
            "added_date": datetime.now().isoformat()
        }
        
        self.signatures["signatures"].append(new_signature)
        self.signatures["last_updated"] = datetime.now().isoformat()
        self.save_signatures()
    
    def add_to_whitelist(self, item_type, value):
        """Add item to whitelist."""
        if item_type in ["hashes", "paths", "processes"]:
            if value not in self.whitelist[item_type]:
                self.whitelist[item_type].append(value)
                self.whitelist["last_updated"] = datetime.now().isoformat()
                self.save_whitelist()
                return True
        return False
    
    def get_signature_count(self):
        """Get total number of signatures."""
        return len(self.signatures.get("signatures", []))
    
    def update_database(self, new_signatures_file):
        """Update database from external file."""
        try:
            with open(new_signatures_file, 'r') as f:
                new_data = json.load(f)
            
            # Merge signatures
            existing_hashes = {sig.get("hash_sha256") for sig in self.signatures.get("signatures", [])}
            
            for new_sig in new_data.get("signatures", []):
                if new_sig.get("hash_sha256") not in existing_hashes:
                    self.signatures["signatures"].append(new_sig)
            
            self.signatures["last_updated"] = datetime.now().isoformat()
            self.save_signatures()
            return True
        except Exception as e:
            print(f"Error updating database: {e}")
            return False
