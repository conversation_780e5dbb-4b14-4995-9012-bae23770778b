"""
Scheduled scan management for the antivirus endpoint agent.
"""

import schedule
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path

class ScanScheduler:
    """Manages scheduled scans for the antivirus agent."""
    
    def __init__(self, scanner, quarantine_manager, logger, config, alert_manager=None):
        self.scanner = scanner
        self.quarantine_manager = quarantine_manager
        self.logger = logger
        self.config = config
        self.alert_manager = alert_manager
        
        # Scheduler thread
        self.scheduler_thread = None
        self.is_running = False
        self.stop_event = threading.Event()
        
        # Scan settings
        self.scan_directories = config.get("scan_directories", [])
        self.scan_schedule = config.get("scan_schedule", {})
        
        # Current scan status
        self.current_scan = None
        self.scan_lock = threading.Lock()
        
        # Setup scheduled scans
        self.setup_schedules()
    
    def setup_schedules(self):
        """Setup scheduled scans based on configuration."""
        schedule.clear()  # Clear existing schedules
        
        # Daily scan
        daily_time = self.scan_schedule.get("daily")
        if daily_time:
            schedule.every().day.at(daily_time).do(self._run_daily_scan)
            self.logger.log_app_event("info", f"Daily scan scheduled at {daily_time}")
        
        # Weekly scan
        weekly_schedule = self.scan_schedule.get("weekly")
        if weekly_schedule:
            try:
                day, time_str = weekly_schedule.split(" ")
                getattr(schedule.every(), day.lower()).at(time_str).do(self._run_weekly_scan)
                self.logger.log_app_event("info", f"Weekly scan scheduled on {day} at {time_str}")
            except (ValueError, AttributeError) as e:
                self.logger.log_app_event("error", f"Invalid weekly schedule format: {weekly_schedule}")
    
    def start_scheduler(self):
        """Start the scan scheduler."""
        if self.is_running:
            return False, "Scheduler is already running"
        
        try:
            self.stop_event.clear()
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            self.is_running = True
            
            self.logger.log_app_event("info", "Scan scheduler started")
            return True, "Scheduler started successfully"
            
        except Exception as e:
            error_msg = f"Error starting scheduler: {e}"
            self.logger.log_app_event("error", error_msg)
            return False, error_msg
    
    def stop_scheduler(self):
        """Stop the scan scheduler."""
        if not self.is_running:
            return False, "Scheduler is not running"
        
        try:
            self.stop_event.set()
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=10)
            
            self.is_running = False
            self.logger.log_app_event("info", "Scan scheduler stopped")
            return True, "Scheduler stopped successfully"
            
        except Exception as e:
            error_msg = f"Error stopping scheduler: {e}"
            self.logger.log_app_event("error", error_msg)
            return False, error_msg
    
    def _scheduler_loop(self):
        """Main scheduler loop."""
        while not self.stop_event.is_set():
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                self.logger.log_app_event("error", f"Error in scheduler loop: {e}")
                time.sleep(60)
    
    def _run_daily_scan(self):
        """Execute daily scan."""
        self.logger.log_app_event("info", "Starting scheduled daily scan")
        
        # Scan user directories and common locations
        scan_dirs = [
            "C:\\Users",
            "C:\\ProgramData",
            "C:\\Temp",
            "C:\\Windows\\Temp"
        ]
        
        self._execute_scheduled_scan("daily", scan_dirs)
    
    def _run_weekly_scan(self):
        """Execute weekly scan."""
        self.logger.log_app_event("info", "Starting scheduled weekly scan")
        
        # Full system scan
        scan_dirs = self.scan_directories
        
        self._execute_scheduled_scan("weekly", scan_dirs)
    
    def _execute_scheduled_scan(self, scan_type, directories):
        """Execute a scheduled scan."""
        with self.scan_lock:
            if self.current_scan:
                self.logger.log_app_event("warning", f"Skipping {scan_type} scan - another scan is running")
                return
            
            self.current_scan = {
                "type": scan_type,
                "start_time": datetime.now(),
                "directories": directories,
                "status": "running"
            }
        
        try:
            all_results = []
            threats_found = 0
            
            for directory in directories:
                if not Path(directory).exists():
                    self.logger.log_app_event("warning", f"Scan directory not found: {directory}")
                    continue
                
                self.logger.log_app_event("info", f"Scanning directory: {directory}")
                
                # Scan directory
                results = self.scanner.scan_directory(directory, recursive=True)
                all_results.extend(results)
                
                # Process results
                for result in results:
                    if result['final_verdict'] in ['malware', 'suspicious']:
                        threats_found += 1
                        
                        # Get threat info
                        threat_info = result.get('signature_result') or result.get('heuristic_result')
                        
                        # Log threat
                        self.logger.log_threat_detection(
                            file_path=result['file_path'],
                            threat_type=threat_info.get('threat_type', 'unknown'),
                            threat_name=threat_info.get('threat_name', 'Scheduled Scan Detection'),
                            action_taken="quarantined",
                            details=threat_info
                        )
                        
                        # Quarantine file
                        success, quarantine_result = self.quarantine_manager.quarantine_file(
                            result['file_path'], threat_info
                        )
                        
                        if success:
                            self.logger.log_app_event(
                                "warning", f"Scheduled scan quarantined: {result['file_path']}"
                            )
                        else:
                            self.logger.log_app_event(
                                "error", f"Failed to quarantine {result['file_path']}: {quarantine_result}"
                            )
            
            # Update scan status
            with self.scan_lock:
                if self.current_scan:
                    self.current_scan.update({
                        "end_time": datetime.now(),
                        "status": "completed",
                        "files_scanned": len(all_results),
                        "threats_found": threats_found
                    })
            
            # Send completion alert
            if self.alert_manager:
                self.alert_manager.send_scan_summary(all_results)
            
            self.logger.log_app_event(
                "info", 
                f"Scheduled {scan_type} scan completed: {len(all_results)} files scanned, {threats_found} threats found"
            )
            
        except Exception as e:
            self.logger.log_app_event("error", f"Error in scheduled {scan_type} scan: {e}")
            
            with self.scan_lock:
                if self.current_scan:
                    self.current_scan.update({
                        "end_time": datetime.now(),
                        "status": "error",
                        "error": str(e)
                    })
        
        finally:
            # Clear current scan
            with self.scan_lock:
                self.current_scan = None
    
    def run_manual_scan(self, directories=None, scan_type="manual"):
        """Run a manual scan."""
        if directories is None:
            directories = self.scan_directories
        
        with self.scan_lock:
            if self.current_scan:
                return False, "Another scan is already running"
        
        # Run scan in separate thread
        scan_thread = threading.Thread(
            target=self._execute_scheduled_scan,
            args=(scan_type, directories),
            daemon=True
        )
        scan_thread.start()
        
        return True, "Manual scan started"
    
    def get_scan_status(self):
        """Get current scan status."""
        with self.scan_lock:
            if self.current_scan:
                return self.current_scan.copy()
            else:
                return None
    
    def is_scan_running(self):
        """Check if a scan is currently running."""
        with self.scan_lock:
            return self.current_scan is not None
    
    def update_schedule(self, daily_time=None, weekly_schedule=None):
        """Update scan schedule."""
        if daily_time:
            self.scan_schedule["daily"] = daily_time
        
        if weekly_schedule:
            self.scan_schedule["weekly"] = weekly_schedule
        
        # Update config
        self.config.set("scan_schedule", self.scan_schedule)
        
        # Restart scheduler with new schedule
        if self.is_running:
            self.setup_schedules()
        
        self.logger.log_app_event("info", "Scan schedule updated")
    
    def get_next_scheduled_scans(self):
        """Get information about next scheduled scans."""
        next_scans = []
        
        for job in schedule.jobs:
            next_run = job.next_run
            if next_run:
                scan_type = "daily" if "daily" in str(job.job_func) else "weekly"
                next_scans.append({
                    "type": scan_type,
                    "next_run": next_run.isoformat(),
                    "time_until": str(next_run - datetime.now())
                })
        
        return next_scans
    
    def cancel_current_scan(self):
        """Cancel the currently running scan."""
        with self.scan_lock:
            if self.current_scan:
                self.current_scan["status"] = "cancelled"
                # Note: This is a simple cancellation flag
                # The actual scan thread will need to check this status
                return True, "Scan cancellation requested"
            else:
                return False, "No scan is currently running"
