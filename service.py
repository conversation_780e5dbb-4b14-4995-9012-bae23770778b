"""
Windows service implementation for the antivirus endpoint agent.
"""

import sys
import os
import time
import threading

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    WINDOWS_SERVICE_AVAILABLE = True
except ImportError:
    WINDOWS_SERVICE_AVAILABLE = False
    print("Windows service modules not available. Install pywin32 to use service functionality.")

from main import AntivirusAgent

class AntivirusService(win32serviceutil.ServiceFramework):
    """Windows service for the antivirus agent."""
    
    _svc_name_ = "AntivirusEndpointAgent"
    _svc_display_name_ = "Antivirus Endpoint Agent"
    _svc_description_ = "Advanced antivirus endpoint protection agent with real-time monitoring and scheduled scanning"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.agent = None
        self.is_running = False
    
    def SvcStop(self):
        """Stop the service."""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        
        try:
            if self.agent:
                self.agent.stop_service()
            self.is_running = False
            win32event.SetEvent(self.hWaitStop)
            
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self._svc_name_, '')
            )
        except Exception as e:
            servicemanager.LogErrorMsg(f"Error stopping service: {e}")
    
    def SvcDoRun(self):
        """Run the service."""
        try:
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, '')
            )
            
            # Initialize and start the antivirus agent
            self.agent = AntivirusAgent()
            
            if self.agent.start_service():
                self.is_running = True
                servicemanager.LogInfoMsg("Antivirus service started successfully")
                
                # Wait for stop signal
                win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
            else:
                servicemanager.LogErrorMsg("Failed to start antivirus service")
                
        except Exception as e:
            servicemanager.LogErrorMsg(f"Error in service main loop: {e}")
            self.SvcStop()

def install_service():
    """Install the Windows service."""
    if not WINDOWS_SERVICE_AVAILABLE:
        print("Error: Windows service modules not available")
        return False
    
    try:
        # Get the path to the current script
        service_script = os.path.abspath(__file__)
        
        # Install the service
        win32serviceutil.InstallService(
            pythonClassString=f"{__name__}.AntivirusService",
            serviceName=AntivirusService._svc_name_,
            displayName=AntivirusService._svc_display_name_,
            description=AntivirusService._svc_description_,
            startType=win32service.SERVICE_AUTO_START
        )
        
        print(f"Service '{AntivirusService._svc_display_name_}' installed successfully")
        print("The service is set to start automatically with Windows")
        print(f"To start the service now, run: net start {AntivirusService._svc_name_}")
        return True
        
    except Exception as e:
        print(f"Error installing service: {e}")
        return False

def uninstall_service():
    """Uninstall the Windows service."""
    if not WINDOWS_SERVICE_AVAILABLE:
        print("Error: Windows service modules not available")
        return False
    
    try:
        # Stop the service if it's running
        try:
            win32serviceutil.StopService(AntivirusService._svc_name_)
            print("Service stopped")
        except:
            pass  # Service might not be running
        
        # Remove the service
        win32serviceutil.RemoveService(AntivirusService._svc_name_)
        print(f"Service '{AntivirusService._svc_display_name_}' uninstalled successfully")
        return True
        
    except Exception as e:
        print(f"Error uninstalling service: {e}")
        return False

def start_service():
    """Start the Windows service."""
    if not WINDOWS_SERVICE_AVAILABLE:
        print("Error: Windows service modules not available")
        return False
    
    try:
        win32serviceutil.StartService(AntivirusService._svc_name_)
        print(f"Service '{AntivirusService._svc_display_name_}' started successfully")
        return True
    except Exception as e:
        print(f"Error starting service: {e}")
        return False

def stop_service():
    """Stop the Windows service."""
    if not WINDOWS_SERVICE_AVAILABLE:
        print("Error: Windows service modules not available")
        return False
    
    try:
        win32serviceutil.StopService(AntivirusService._svc_name_)
        print(f"Service '{AntivirusService._svc_display_name_}' stopped successfully")
        return True
    except Exception as e:
        print(f"Error stopping service: {e}")
        return False

def get_service_status():
    """Get the status of the Windows service."""
    if not WINDOWS_SERVICE_AVAILABLE:
        return "Windows service modules not available"
    
    try:
        status = win32serviceutil.QueryServiceStatus(AntivirusService._svc_name_)
        status_map = {
            win32service.SERVICE_STOPPED: "Stopped",
            win32service.SERVICE_START_PENDING: "Starting",
            win32service.SERVICE_STOP_PENDING: "Stopping",
            win32service.SERVICE_RUNNING: "Running",
            win32service.SERVICE_CONTINUE_PENDING: "Continuing",
            win32service.SERVICE_PAUSE_PENDING: "Pausing",
            win32service.SERVICE_PAUSED: "Paused"
        }
        return status_map.get(status[1], f"Unknown ({status[1]})")
    except Exception as e:
        return f"Error getting status: {e}"

class TaskSchedulerManager:
    """Manages Windows Task Scheduler integration as an alternative to Windows Service."""
    
    def __init__(self):
        self.task_name = "AntivirusEndpointAgent"
        self.script_path = os.path.abspath("main.py")
        self.python_path = sys.executable
    
    def create_startup_task(self):
        """Create a Windows Task Scheduler task to run at startup."""
        try:
            import subprocess
            
            # XML task definition
            task_xml = f'''<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <RegistrationInfo>
    <Date>2024-01-01T00:00:00</Date>
    <Author>Antivirus Endpoint Agent</Author>
    <Description>Antivirus endpoint protection with real-time monitoring</Description>
  </RegistrationInfo>
  <Triggers>
    <BootTrigger>
      <Enabled>true</Enabled>
    </BootTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <UserId>S-1-5-18</UserId>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>{self.python_path}</Command>
      <Arguments>{self.script_path} --service</Arguments>
      <WorkingDirectory>{os.path.dirname(self.script_path)}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>'''
            
            # Save XML to temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as f:
                f.write(task_xml)
                xml_file = f.name
            
            try:
                # Create the task
                cmd = [
                    'schtasks', '/create',
                    '/tn', self.task_name,
                    '/xml', xml_file,
                    '/f'  # Force overwrite if exists
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                print(f"Task '{self.task_name}' created successfully")
                return True
                
            finally:
                # Clean up temporary file
                try:
                    os.unlink(xml_file)
                except:
                    pass
            
        except subprocess.CalledProcessError as e:
            print(f"Error creating task: {e.stderr}")
            return False
        except Exception as e:
            print(f"Error creating task: {e}")
            return False
    
    def delete_task(self):
        """Delete the scheduled task."""
        try:
            import subprocess
            
            cmd = ['schtasks', '/delete', '/tn', self.task_name, '/f']
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"Task '{self.task_name}' deleted successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"Error deleting task: {e.stderr}")
            return False
        except Exception as e:
            print(f"Error deleting task: {e}")
            return False
    
    def start_task(self):
        """Start the scheduled task."""
        try:
            import subprocess
            
            cmd = ['schtasks', '/run', '/tn', self.task_name]
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"Task '{self.task_name}' started successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"Error starting task: {e.stderr}")
            return False
        except Exception as e:
            print(f"Error starting task: {e}")
            return False
    
    def get_task_status(self):
        """Get the status of the scheduled task."""
        try:
            import subprocess
            
            cmd = ['schtasks', '/query', '/tn', self.task_name, '/fo', 'csv']
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Parse CSV output
            lines = result.stdout.strip().split('\n')
            if len(lines) >= 2:
                # Second line contains the task info
                fields = lines[1].split(',')
                if len(fields) >= 4:
                    status = fields[3].strip('"')
                    return status
            
            return "Unknown"
            
        except subprocess.CalledProcessError:
            return "Not found"
        except Exception as e:
            return f"Error: {e}"

def main():
    """Main entry point for service management."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python service.py install    - Install Windows service")
        print("  python service.py uninstall  - Uninstall Windows service")
        print("  python service.py start      - Start Windows service")
        print("  python service.py stop       - Stop Windows service")
        print("  python service.py status     - Get service status")
        print("  python service.py task-install - Install as scheduled task")
        print("  python service.py task-uninstall - Remove scheduled task")
        print("  python service.py task-start - Start scheduled task")
        print("  python service.py task-status - Get task status")
        return
    
    command = sys.argv[1].lower()
    
    if command == "install":
        install_service()
    elif command == "uninstall":
        uninstall_service()
    elif command == "start":
        start_service()
    elif command == "stop":
        stop_service()
    elif command == "status":
        status = get_service_status()
        print(f"Service status: {status}")
    elif command == "task-install":
        task_manager = TaskSchedulerManager()
        task_manager.create_startup_task()
    elif command == "task-uninstall":
        task_manager = TaskSchedulerManager()
        task_manager.delete_task()
    elif command == "task-start":
        task_manager = TaskSchedulerManager()
        task_manager.start_task()
    elif command == "task-status":
        task_manager = TaskSchedulerManager()
        status = task_manager.get_task_status()
        print(f"Task status: {status}")
    else:
        print(f"Unknown command: {command}")

if __name__ == '__main__':
    if WINDOWS_SERVICE_AVAILABLE and len(sys.argv) == 1:
        # Running as service
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(AntivirusService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # Command line mode
        main()
