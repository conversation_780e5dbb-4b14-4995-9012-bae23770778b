"""
Performance monitoring script for the antivirus endpoint agent.
"""

import psutil
import time
import sys
import os
from datetime import datetime
import json

class PerformanceMonitor:
    """Monitor system performance and antivirus agent resource usage."""
    
    def __init__(self, process_name="python.exe"):
        self.process_name = process_name
        self.monitoring = False
        self.stats = []
    
    def find_antivirus_processes(self):
        """Find running antivirus agent processes."""
        processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'].lower() == self.process_name.lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'main.py' in cmdline or 'AntivirusAgent' in cmdline:
                        processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return processes
    
    def get_system_stats(self):
        """Get current system performance statistics."""
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'memory_available_mb': psutil.virtual_memory().available / (1024 * 1024),
            'disk_usage_percent': psutil.disk_usage('C:\\').percent,
            'disk_free_gb': psutil.disk_usage('C:\\').free / (1024 * 1024 * 1024)
        }
    
    def get_process_stats(self, process):
        """Get statistics for a specific process."""
        try:
            with process.oneshot():
                return {
                    'pid': process.pid,
                    'cpu_percent': process.cpu_percent(),
                    'memory_mb': process.memory_info().rss / (1024 * 1024),
                    'memory_percent': process.memory_percent(),
                    'num_threads': process.num_threads(),
                    'num_handles': process.num_handles() if hasattr(process, 'num_handles') else 0,
                    'status': process.status(),
                    'create_time': datetime.fromtimestamp(process.create_time()).isoformat()
                }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return None
    
    def monitor_performance(self, duration_minutes=10, interval_seconds=30):
        """Monitor performance for specified duration."""
        print(f"Starting performance monitoring for {duration_minutes} minutes...")
        print(f"Monitoring interval: {interval_seconds} seconds")
        print("-" * 60)
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        self.monitoring = True
        self.stats = []
        
        try:
            while time.time() < end_time and self.monitoring:
                # Get system stats
                system_stats = self.get_system_stats()
                
                # Find antivirus processes
                av_processes = self.find_antivirus_processes()
                
                # Get process stats
                process_stats = []
                for proc in av_processes:
                    proc_stats = self.get_process_stats(proc)
                    if proc_stats:
                        process_stats.append(proc_stats)
                
                # Combine stats
                combined_stats = {
                    'system': system_stats,
                    'antivirus_processes': process_stats
                }
                
                self.stats.append(combined_stats)
                
                # Print current stats
                self.print_current_stats(system_stats, process_stats)
                
                time.sleep(interval_seconds)
        
        except KeyboardInterrupt:
            print("\nMonitoring stopped by user")
        
        self.monitoring = False
        print(f"\nMonitoring completed. Collected {len(self.stats)} data points.")
        
        return self.stats
    
    def print_current_stats(self, system_stats, process_stats):
        """Print current performance statistics."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        print(f"[{timestamp}] System - CPU: {system_stats['cpu_percent']:.1f}% | "
              f"Memory: {system_stats['memory_percent']:.1f}% | "
              f"Disk: {system_stats['disk_usage_percent']:.1f}%")
        
        if process_stats:
            total_memory = sum(p['memory_mb'] for p in process_stats)
            total_cpu = sum(p['cpu_percent'] for p in process_stats)
            
            print(f"         Antivirus - Processes: {len(process_stats)} | "
                  f"CPU: {total_cpu:.1f}% | Memory: {total_memory:.1f}MB")
        else:
            print("         Antivirus - No processes found")
        
        print()
    
    def generate_report(self, output_file="performance_report.json"):
        """Generate performance report."""
        if not self.stats:
            print("No performance data available")
            return
        
        # Calculate averages and peaks
        system_cpu_avg = sum(s['system']['cpu_percent'] for s in self.stats) / len(self.stats)
        system_memory_avg = sum(s['system']['memory_percent'] for s in self.stats) / len(self.stats)
        
        system_cpu_peak = max(s['system']['cpu_percent'] for s in self.stats)
        system_memory_peak = max(s['system']['memory_percent'] for s in self.stats)
        
        # Antivirus process stats
        av_memory_values = []
        av_cpu_values = []
        
        for stat in self.stats:
            if stat['antivirus_processes']:
                total_memory = sum(p['memory_mb'] for p in stat['antivirus_processes'])
                total_cpu = sum(p['cpu_percent'] for p in stat['antivirus_processes'])
                av_memory_values.append(total_memory)
                av_cpu_values.append(total_cpu)
        
        report = {
            'monitoring_summary': {
                'start_time': self.stats[0]['system']['timestamp'] if self.stats else None,
                'end_time': self.stats[-1]['system']['timestamp'] if self.stats else None,
                'data_points': len(self.stats),
                'duration_minutes': len(self.stats) * 0.5  # Assuming 30-second intervals
            },
            'system_performance': {
                'cpu_average': round(system_cpu_avg, 2),
                'cpu_peak': round(system_cpu_peak, 2),
                'memory_average': round(system_memory_avg, 2),
                'memory_peak': round(system_memory_peak, 2)
            },
            'antivirus_performance': {
                'memory_average_mb': round(sum(av_memory_values) / len(av_memory_values), 2) if av_memory_values else 0,
                'memory_peak_mb': round(max(av_memory_values), 2) if av_memory_values else 0,
                'cpu_average': round(sum(av_cpu_values) / len(av_cpu_values), 2) if av_cpu_values else 0,
                'cpu_peak': round(max(av_cpu_values), 2) if av_cpu_values else 0,
                'process_count_avg': round(sum(len(s['antivirus_processes']) for s in self.stats) / len(self.stats), 1)
            },
            'raw_data': self.stats
        }
        
        # Save report
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Performance report saved to: {output_file}")
        
        # Print summary
        self.print_summary(report)
        
        return report
    
    def print_summary(self, report):
        """Print performance summary."""
        print("\n" + "=" * 60)
        print("PERFORMANCE MONITORING SUMMARY")
        print("=" * 60)
        
        summary = report['monitoring_summary']
        system = report['system_performance']
        antivirus = report['antivirus_performance']
        
        print(f"Monitoring Duration: {summary['duration_minutes']:.1f} minutes")
        print(f"Data Points Collected: {summary['data_points']}")
        
        print(f"\nSystem Performance:")
        print(f"  CPU Usage - Average: {system['cpu_average']:.1f}% | Peak: {system['cpu_peak']:.1f}%")
        print(f"  Memory Usage - Average: {system['memory_average']:.1f}% | Peak: {system['memory_peak']:.1f}%")
        
        print(f"\nAntivirus Agent Performance:")
        print(f"  Memory Usage - Average: {antivirus['memory_average_mb']:.1f}MB | Peak: {antivirus['memory_peak_mb']:.1f}MB")
        print(f"  CPU Usage - Average: {antivirus['cpu_average']:.1f}% | Peak: {antivirus['cpu_peak']:.1f}%")
        print(f"  Average Process Count: {antivirus['process_count_avg']:.1f}")
        
        # Performance assessment
        print(f"\nPerformance Assessment:")
        
        if antivirus['memory_average_mb'] > 500:
            print("  ⚠️  High memory usage detected")
        elif antivirus['memory_average_mb'] > 200:
            print("  ⚡ Moderate memory usage")
        else:
            print("  ✅ Low memory usage")
        
        if antivirus['cpu_average'] > 10:
            print("  ⚠️  High CPU usage detected")
        elif antivirus['cpu_average'] > 5:
            print("  ⚡ Moderate CPU usage")
        else:
            print("  ✅ Low CPU usage")
        
        print("=" * 60)
    
    def real_time_monitor(self):
        """Real-time performance monitoring with live updates."""
        print("Real-time Performance Monitor")
        print("Press Ctrl+C to stop")
        print("-" * 40)
        
        try:
            while True:
                system_stats = self.get_system_stats()
                av_processes = self.find_antivirus_processes()
                
                process_stats = []
                for proc in av_processes:
                    proc_stats = self.get_process_stats(proc)
                    if proc_stats:
                        process_stats.append(proc_stats)
                
                # Clear screen (Windows)
                os.system('cls' if os.name == 'nt' else 'clear')
                
                print("Real-time Performance Monitor")
                print("Press Ctrl+C to stop")
                print("-" * 40)
                
                self.print_current_stats(system_stats, process_stats)
                
                time.sleep(2)
        
        except KeyboardInterrupt:
            print("\nReal-time monitoring stopped")

def main():
    """Main function."""
    monitor = PerformanceMonitor()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "realtime":
            monitor.real_time_monitor()
        
        elif command == "monitor":
            duration = int(sys.argv[2]) if len(sys.argv) > 2 else 10
            interval = int(sys.argv[3]) if len(sys.argv) > 3 else 30
            
            stats = monitor.monitor_performance(duration, interval)
            monitor.generate_report()
        
        elif command == "check":
            # Quick check
            system_stats = monitor.get_system_stats()
            av_processes = monitor.find_antivirus_processes()
            
            process_stats = []
            for proc in av_processes:
                proc_stats = monitor.get_process_stats(proc)
                if proc_stats:
                    process_stats.append(proc_stats)
            
            print("Current Performance Status:")
            print("-" * 30)
            monitor.print_current_stats(system_stats, process_stats)
        
        else:
            print("Unknown command. Available commands:")
            print("  realtime - Real-time monitoring")
            print("  monitor [duration_minutes] [interval_seconds] - Timed monitoring")
            print("  check - Quick status check")
    
    else:
        print("Performance Monitor for Antivirus Endpoint Agent")
        print("Usage:")
        print("  python performance_monitor.py realtime")
        print("  python performance_monitor.py monitor [duration] [interval]")
        print("  python performance_monitor.py check")

if __name__ == "__main__":
    main()
