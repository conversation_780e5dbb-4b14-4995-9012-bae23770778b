"""
Service installation and management script.
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def check_admin():
    """Check if running with administrator privileges."""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def install_dependencies():
    """Install required Python packages."""
    print("Installing required dependencies...")
    
    packages = [
        'watchdog==3.0.0',
        'schedule==1.2.0',
        'psutil==5.9.6',
        'pywin32==306',
        'plyer==2.1.0',
        'cryptography==41.0.7'
    ]
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✓ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install {package}: {e}")
            return False
    
    print("All dependencies installed successfully!")
    return True

def install_windows_service():
    """Install as Windows service."""
    if not check_admin():
        print("Error: Administrator privileges required to install Windows service")
        print("Please run this script as Administrator")
        return False
    
    try:
        from service import install_service
        return install_service()
    except ImportError as e:
        print(f"Error importing service module: {e}")
        print("Make sure pywin32 is installed: pip install pywin32")
        return False

def install_task_scheduler():
    """Install as Windows Task Scheduler task."""
    if not check_admin():
        print("Error: Administrator privileges required to create scheduled tasks")
        print("Please run this script as Administrator")
        return False
    
    try:
        from service import TaskSchedulerManager
        task_manager = TaskSchedulerManager()
        return task_manager.create_startup_task()
    except Exception as e:
        print(f"Error creating scheduled task: {e}")
        return False

def uninstall_windows_service():
    """Uninstall Windows service."""
    if not check_admin():
        print("Error: Administrator privileges required to uninstall Windows service")
        return False
    
    try:
        from service import uninstall_service
        return uninstall_service()
    except ImportError as e:
        print(f"Error importing service module: {e}")
        return False

def uninstall_task_scheduler():
    """Uninstall Task Scheduler task."""
    if not check_admin():
        print("Error: Administrator privileges required to remove scheduled tasks")
        return False
    
    try:
        from service import TaskSchedulerManager
        task_manager = TaskSchedulerManager()
        return task_manager.delete_task()
    except Exception as e:
        print(f"Error removing scheduled task: {e}")
        return False

def setup_directories():
    """Create necessary directories."""
    directories = ['logs', 'quarantine', 'data']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"Created directory: {directory}")

def create_desktop_shortcuts():
    """Create desktop shortcuts for common operations."""
    try:
        import win32com.client
        
        desktop = Path.home() / "Desktop"
        shell = win32com.client.Dispatch("WScript.Shell")
        
        # Main application shortcut
        shortcut = shell.CreateShortCut(str(desktop / "Antivirus Agent.lnk"))
        shortcut.Targetpath = str(Path.cwd() / "main.py")
        shortcut.Arguments = "--status"
        shortcut.WorkingDirectory = str(Path.cwd())
        shortcut.IconLocation = str(Path.cwd() / "main.py")
        shortcut.save()
        
        # Quick scan shortcut
        shortcut = shell.CreateShortCut(str(desktop / "Quick Scan.lnk"))
        shortcut.Targetpath = str(Path.cwd() / "main.py")
        shortcut.Arguments = "--quick-scan"
        shortcut.WorkingDirectory = str(Path.cwd())
        shortcut.save()
        
        print("Desktop shortcuts created successfully!")
        return True
        
    except ImportError:
        print("Warning: Could not create desktop shortcuts (pywin32 not available)")
        return False
    except Exception as e:
        print(f"Warning: Could not create desktop shortcuts: {e}")
        return False

def run_initial_scan():
    """Run initial system scan."""
    print("Running initial quick scan...")
    try:
        from main import AntivirusAgent
        agent = AntivirusAgent()
        
        # Quick scan of common directories
        quick_dirs = [
            str(Path.home() / "Downloads"),
            str(Path.home() / "Desktop"),
            "C:\\Temp"
        ]
        
        for directory in quick_dirs:
            if Path(directory).exists():
                print(f"Scanning {directory}...")
                results = agent.scanner.scan_directory(directory, recursive=False)
                threats = sum(1 for r in results if r['final_verdict'] in ['malware', 'suspicious'])
                print(f"  Scanned {len(results)} files, found {threats} threats")
        
        print("Initial scan completed!")
        return True
        
    except Exception as e:
        print(f"Error during initial scan: {e}")
        return False

def main():
    """Main installation function."""
    parser = argparse.ArgumentParser(description="Antivirus Endpoint Agent Installer")
    parser.add_argument("--service", action="store_true", help="Install as Windows service")
    parser.add_argument("--task", action="store_true", help="Install as scheduled task")
    parser.add_argument("--uninstall", action="store_true", help="Uninstall service/task")
    parser.add_argument("--deps", action="store_true", help="Install dependencies only")
    parser.add_argument("--full", action="store_true", help="Full installation")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("Antivirus Endpoint Agent Installer")
    print("=" * 60)
    
    if args.deps:
        return 0 if install_dependencies() else 1
    
    if args.uninstall:
        print("Uninstalling Antivirus Endpoint Agent...")
        success = True
        
        # Try to uninstall both service and task
        try:
            uninstall_windows_service()
        except:
            pass
        
        try:
            uninstall_task_scheduler()
        except:
            pass
        
        print("Uninstallation completed!")
        return 0
    
    # Default or full installation
    if args.full or not any([args.service, args.task]):
        print("Starting full installation...")
        
        # Step 1: Install dependencies
        if not install_dependencies():
            print("Failed to install dependencies!")
            return 1
        
        # Step 2: Setup directories
        setup_directories()
        
        # Step 3: Install service (prefer service over task)
        if args.service or not args.task:
            print("\nInstalling as Windows service...")
            if install_windows_service():
                print("Windows service installed successfully!")
            else:
                print("Failed to install Windows service, trying Task Scheduler...")
                if install_task_scheduler():
                    print("Task Scheduler installation successful!")
                else:
                    print("Failed to install via Task Scheduler!")
                    return 1
        else:
            print("\nInstalling as scheduled task...")
            if not install_task_scheduler():
                print("Failed to install scheduled task!")
                return 1
        
        # Step 4: Create shortcuts
        create_desktop_shortcuts()
        
        # Step 5: Run initial scan
        run_initial_scan()
        
        print("\n" + "=" * 60)
        print("Installation completed successfully!")
        print("=" * 60)
        print("The Antivirus Endpoint Agent is now installed and ready to use.")
        print("\nTo start the service:")
        print("  net start AntivirusEndpointAgent")
        print("\nTo check status:")
        print("  python main.py --status")
        print("\nTo run a quick scan:")
        print("  python main.py --quick-scan")
        print("=" * 60)
        
        return 0
    
    elif args.service:
        return 0 if install_windows_service() else 1
    
    elif args.task:
        return 0 if install_task_scheduler() else 1
    
    else:
        parser.print_help()
        return 0

if __name__ == "__main__":
    sys.exit(main())
