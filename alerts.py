"""
Alert and notification system for the antivirus endpoint agent.
"""

import os
import sys
import threading
import time
from datetime import datetime

# Try to import Windows-specific modules
try:
    import win32api
    import win32con
    import win32gui
    WINDOWS_AVAILABLE = True
except ImportError:
    WINDOWS_AVAILABLE = False

# Try to import cross-platform notification library
try:
    from plyer import notification
    PLYER_AVAILABLE = True
except ImportError:
    PLYER_AVAILABLE = False

class AlertManager:
    """Manages alerts and notifications for threat detection."""
    
    def __init__(self, logger=None, config=None):
        self.logger = logger
        self.config = config or {}
        self.alert_enabled = self.config.get("alert_enabled", True)
        
        # Alert history to prevent spam
        self.recent_alerts = {}
        self.alert_cooldown = 300  # 5 minutes cooldown per file
        
        # Lock for thread safety
        self.lock = threading.Lock()
    
    def send_threat_alert(self, file_path, threat_info):
        """Send threat detection alert."""
        if not self.alert_enabled:
            return
        
        with self.lock:
            # Check cooldown
            current_time = time.time()
            file_key = str(file_path)
            
            if file_key in self.recent_alerts:
                if current_time - self.recent_alerts[file_key] < self.alert_cooldown:
                    return  # Skip alert due to cooldown
            
            self.recent_alerts[file_key] = current_time
        
        # Prepare alert message
        threat_name = threat_info.get('threat_name', 'Unknown Threat')
        threat_type = threat_info.get('threat_type', 'Unknown')
        severity = threat_info.get('severity', 'medium')
        
        title = f"🛡️ Threat Detected - {severity.upper()}"
        message = f"File: {os.path.basename(file_path)}\nThreat: {threat_name}\nType: {threat_type}\nAction: Quarantined"
        
        # Try different notification methods
        success = False
        
        # Method 1: Windows balloon notification
        if WINDOWS_AVAILABLE and not success:
            success = self._send_windows_balloon(title, message)
        
        # Method 2: Plyer notification
        if PLYER_AVAILABLE and not success:
            success = self._send_plyer_notification(title, message)
        
        # Method 3: Console alert (fallback)
        if not success:
            self._send_console_alert(title, message, file_path, threat_info)
        
        # Log the alert
        if self.logger:
            self.logger.log_app_event("info", f"Threat alert sent for {file_path}")
    
    def _send_windows_balloon(self, title, message):
        """Send Windows balloon notification."""
        try:
            # Create a temporary window for the notification
            class_name = "AntivirusAlert"
            window_name = "Antivirus Alert"
            
            # Register window class
            wc = win32gui.WNDCLASS()
            wc.lpfnWndProc = win32gui.DefWindowProc
            wc.lpszClassName = class_name
            wc.hInstance = win32api.GetModuleHandle(None)
            
            try:
                win32gui.RegisterClass(wc)
            except:
                pass  # Class might already be registered
            
            # Create window
            hwnd = win32gui.CreateWindow(
                class_name, window_name,
                0, 0, 0, 0, 0, 0, 0,
                win32api.GetModuleHandle(None), None
            )
            
            # Show balloon notification
            win32gui.Shell_NotifyIcon(
                win32gui.NIM_ADD,
                (hwnd, 0, win32gui.NIF_INFO | win32gui.NIF_MESSAGE,
                 0, 0, "", title, message, 5000, "")
            )
            
            # Clean up after a delay
            def cleanup():
                time.sleep(6)
                try:
                    win32gui.Shell_NotifyIcon(win32gui.NIM_DELETE, (hwnd, 0))
                    win32gui.DestroyWindow(hwnd)
                except:
                    pass
            
            threading.Thread(target=cleanup, daemon=True).start()
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.log_app_event("debug", f"Windows balloon notification failed: {e}")
            return False
    
    def _send_plyer_notification(self, title, message):
        """Send notification using plyer library."""
        try:
            notification.notify(
                title=title,
                message=message,
                app_name="Antivirus Agent",
                timeout=10
            )
            return True
        except Exception as e:
            if self.logger:
                self.logger.log_app_event("debug", f"Plyer notification failed: {e}")
            return False
    
    def _send_console_alert(self, title, message, file_path, threat_info):
        """Send console alert as fallback."""
        print("\n" + "=" * 60)
        print(f"🚨 {title}")
        print("=" * 60)
        print(f"📁 File: {file_path}")
        print(f"🦠 Threat: {threat_info.get('threat_name', 'Unknown')}")
        print(f"📊 Type: {threat_info.get('threat_type', 'Unknown')}")
        print(f"⚠️  Severity: {threat_info.get('severity', 'Unknown')}")
        print(f"🛡️ Action: File has been quarantined")
        print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Additional details for heuristic detection
        if 'indicators' in threat_info:
            print(f"🔍 Indicators:")
            for indicator in threat_info['indicators']:
                print(f"   • {indicator}")
        
        print("=" * 60)
        
        # Beep sound if available
        try:
            if sys.platform == "win32":
                import winsound
                winsound.Beep(1000, 500)  # 1000 Hz for 500ms
        except:
            pass
    
    def send_system_alert(self, alert_type, message, details=None):
        """Send system-level alerts (service start/stop, errors, etc.)."""
        if not self.alert_enabled:
            return
        
        title_map = {
            "service_start": "🟢 Antivirus Service Started",
            "service_stop": "🔴 Antivirus Service Stopped",
            "service_error": "❌ Antivirus Service Error",
            "database_update": "📥 Database Updated",
            "scan_complete": "✅ Scan Complete",
            "quarantine_full": "⚠️ Quarantine Storage Warning"
        }
        
        title = title_map.get(alert_type, "ℹ️ Antivirus Notification")
        
        # For system alerts, use less intrusive methods
        if alert_type in ["service_start", "database_update", "scan_complete"]:
            # Only log these, don't show popup
            if self.logger:
                self.logger.log_app_event("info", f"System alert: {message}")
        else:
            # Show notification for important alerts
            if PLYER_AVAILABLE:
                try:
                    notification.notify(
                        title=title,
                        message=message,
                        app_name="Antivirus Agent",
                        timeout=5
                    )
                except:
                    pass
            
            # Always log
            if self.logger:
                self.logger.log_app_event("warning", f"System alert: {message}")
    
    def send_scan_summary(self, scan_results):
        """Send scan completion summary."""
        if not scan_results:
            return
        
        total_files = len(scan_results)
        threats_found = sum(1 for result in scan_results 
                          if result['final_verdict'] in ['malware', 'suspicious'])
        
        if threats_found > 0:
            title = f"🛡️ Scan Complete - {threats_found} Threats Found"
            message = f"Scanned {total_files} files\nFound {threats_found} threats\nAll threats quarantined"
            
            if PLYER_AVAILABLE:
                try:
                    notification.notify(
                        title=title,
                        message=message,
                        app_name="Antivirus Agent",
                        timeout=10
                    )
                except:
                    pass
        
        # Log summary
        if self.logger:
            self.logger.log_app_event(
                "info", 
                f"Scan summary: {total_files} files scanned, {threats_found} threats found"
            )
    
    def enable_alerts(self):
        """Enable alert notifications."""
        self.alert_enabled = True
        if self.config:
            self.config.set("alert_enabled", True)
    
    def disable_alerts(self):
        """Disable alert notifications."""
        self.alert_enabled = False
        if self.config:
            self.config.set("alert_enabled", False)
    
    def is_alerts_enabled(self):
        """Check if alerts are enabled."""
        return self.alert_enabled
    
    def clear_alert_history(self):
        """Clear alert cooldown history."""
        with self.lock:
            self.recent_alerts.clear()
    
    def get_alert_capabilities(self):
        """Get available alert methods."""
        capabilities = {
            "console": True,
            "windows_balloon": WINDOWS_AVAILABLE,
            "cross_platform": PLYER_AVAILABLE
        }
        return capabilities
