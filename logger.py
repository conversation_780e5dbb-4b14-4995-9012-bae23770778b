"""
Logging system for the antivirus endpoint agent.
"""

import logging
import os
from datetime import datetime
from pathlib import Path
import json

class AntivirusLogger:
    """Enhanced logging system for antivirus operations."""
    
    def __init__(self, log_directory="logs", log_level="INFO"):
        self.log_directory = log_directory
        self.log_level = getattr(logging, log_level.upper())
        
        # Create log directory
        os.makedirs(log_directory, exist_ok=True)
        
        # Setup loggers
        self.setup_loggers()
    
    def setup_loggers(self):
        """Setup different loggers for different purposes."""
        
        # Main application logger
        self.app_logger = self._create_logger(
            "antivirus_app",
            os.path.join(self.log_directory, "antivirus.log")
        )
        
        # Scan results logger
        self.scan_logger = self._create_logger(
            "scan_results",
            os.path.join(self.log_directory, "scan_results.log")
        )
        
        # Threat detection logger
        self.threat_logger = self._create_logger(
            "threats",
            os.path.join(self.log_directory, "threats.log")
        )
        
        # Quarantine operations logger
        self.quarantine_logger = self._create_logger(
            "quarantine",
            os.path.join(self.log_directory, "quarantine.log")
        )
    
    def _create_logger(self, name, log_file):
        """Create a logger with file and console handlers."""
        logger = logging.getLogger(name)
        logger.setLevel(self.log_level)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(self.log_level)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def log_app_event(self, level, message, **kwargs):
        """Log application events."""
        extra_info = json.dumps(kwargs) if kwargs else ""
        full_message = f"{message} {extra_info}".strip()
        getattr(self.app_logger, level.lower())(full_message)
    
    def log_scan_result(self, file_path, scan_type, result, threat_level=None, details=None):
        """Log scan results."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "file_path": str(file_path),
            "scan_type": scan_type,
            "result": result,
            "threat_level": threat_level,
            "details": details
        }
        self.scan_logger.info(json.dumps(log_entry))
    
    def log_threat_detection(self, file_path, threat_type, threat_name, action_taken, details=None):
        """Log threat detections."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "file_path": str(file_path),
            "threat_type": threat_type,
            "threat_name": threat_name,
            "action_taken": action_taken,
            "details": details
        }
        self.threat_logger.warning(json.dumps(log_entry))
    
    def log_quarantine_action(self, action, file_path, destination=None, success=True, error=None):
        """Log quarantine operations."""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "file_path": str(file_path),
            "destination": str(destination) if destination else None,
            "success": success,
            "error": str(error) if error else None
        }
        level = "info" if success else "error"
        getattr(self.quarantine_logger, level)(json.dumps(log_entry))
    
    def get_recent_threats(self, hours=24):
        """Get recent threat detections."""
        threats = []
        threat_log_file = os.path.join(self.log_directory, "threats.log")
        
        if not os.path.exists(threat_log_file):
            return threats
        
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        
        try:
            with open(threat_log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        # Extract JSON part from log line
                        json_start = line.find('{')
                        if json_start != -1:
                            json_data = line[json_start:].strip()
                            threat_data = json.loads(json_data)
                            
                            # Check if within time range
                            threat_time = datetime.fromisoformat(threat_data['timestamp']).timestamp()
                            if threat_time >= cutoff_time:
                                threats.append(threat_data)
                    except (json.JSONDecodeError, KeyError):
                        continue
        except Exception as e:
            self.log_app_event("error", f"Error reading threat log: {e}")
        
        return threats
