"""
PyInstaller build script for creating standalone executable.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def build_executable():
    """Build standalone executable using PyInstaller."""
    
    print("Building Antivirus Endpoint Agent executable...")
    
    # PyInstaller command
    cmd = [
        'pyinstaller',
        '--onefile',                    # Single executable file
        '--windowed',                   # No console window (for service mode)
        '--name=AntivirusAgent',        # Output name
        '--icon=icon.ico',              # Icon file (if available)
        '--add-data=data;data',         # Include data directory
        '--add-data=config.json;.',     # Include config file
        '--hidden-import=win32serviceutil',
        '--hidden-import=win32service',
        '--hidden-import=win32event',
        '--hidden-import=servicemanager',
        '--hidden-import=plyer.platforms.win.notification',
        '--hidden-import=watchdog.observers.polling',
        '--hidden-import=watchdog.observers.winapi',
        'main.py'
    ]
    
    try:
        # Run PyInstaller
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Build successful!")
        print(f"Executable created: dist/AntivirusAgent.exe")
        
        # Copy additional files to dist directory
        dist_dir = Path("dist")
        if dist_dir.exists():
            # Copy data directory
            if Path("data").exists():
                shutil.copytree("data", dist_dir / "data", dirs_exist_ok=True)
                print("Copied data directory to dist/")
            
            # Copy config file
            if Path("config.json").exists():
                shutil.copy2("config.json", dist_dir / "config.json")
                print("Copied config.json to dist/")
            
            # Create batch files for easy service management
            create_batch_files(dist_dir)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False
    except FileNotFoundError:
        print("Error: PyInstaller not found. Install it with: pip install pyinstaller")
        return False

def create_batch_files(dist_dir):
    """Create batch files for service management."""
    
    # Install service batch file
    install_bat = dist_dir / "install_service.bat"
    with open(install_bat, 'w') as f:
        f.write("""@echo off
echo Installing Antivirus Endpoint Agent Service...
AntivirusAgent.exe --install-service
if %errorlevel% == 0 (
    echo Service installed successfully!
    echo To start the service, run: net start AntivirusEndpointAgent
) else (
    echo Failed to install service. Make sure you're running as Administrator.
)
pause
""")
    
    # Uninstall service batch file
    uninstall_bat = dist_dir / "uninstall_service.bat"
    with open(uninstall_bat, 'w') as f:
        f.write("""@echo off
echo Uninstalling Antivirus Endpoint Agent Service...
net stop AntivirusEndpointAgent 2>nul
AntivirusAgent.exe --uninstall-service
if %errorlevel% == 0 (
    echo Service uninstalled successfully!
) else (
    echo Failed to uninstall service. Make sure you're running as Administrator.
)
pause
""")
    
    # Start service batch file
    start_bat = dist_dir / "start_service.bat"
    with open(start_bat, 'w') as f:
        f.write("""@echo off
echo Starting Antivirus Endpoint Agent Service...
net start AntivirusEndpointAgent
if %errorlevel% == 0 (
    echo Service started successfully!
) else (
    echo Failed to start service. Make sure the service is installed.
)
pause
""")
    
    # Stop service batch file
    stop_bat = dist_dir / "stop_service.bat"
    with open(stop_bat, 'w') as f:
        f.write("""@echo off
echo Stopping Antivirus Endpoint Agent Service...
net stop AntivirusEndpointAgent
if %errorlevel% == 0 (
    echo Service stopped successfully!
) else (
    echo Failed to stop service.
)
pause
""")
    
    # Run in foreground batch file
    run_bat = dist_dir / "run_foreground.bat"
    with open(run_bat, 'w') as f:
        f.write("""@echo off
echo Starting Antivirus Agent in foreground mode...
echo Press Ctrl+C to stop
AntivirusAgent.exe --service
pause
""")
    
    # Quick scan batch file
    quick_scan_bat = dist_dir / "quick_scan.bat"
    with open(quick_scan_bat, 'w') as f:
        f.write("""@echo off
echo Running Quick Scan...
AntivirusAgent.exe --quick-scan
pause
""")
    
    # Status check batch file
    status_bat = dist_dir / "check_status.bat"
    with open(status_bat, 'w') as f:
        f.write("""@echo off
echo Checking Antivirus Agent Status...
AntivirusAgent.exe --status
pause
""")
    
    print("Created batch files for service management in dist/")

def build_console_version():
    """Build console version for debugging."""
    
    print("Building console version...")
    
    cmd = [
        'pyinstaller',
        '--onefile',
        '--console',                    # Show console window
        '--name=AntivirusAgent-Console',
        '--add-data=data;data',
        '--add-data=config.json;.',
        '--hidden-import=win32serviceutil',
        '--hidden-import=win32service',
        '--hidden-import=win32event',
        '--hidden-import=servicemanager',
        '--hidden-import=plyer.platforms.win.notification',
        '--hidden-import=watchdog.observers.polling',
        '--hidden-import=watchdog.observers.winapi',
        'main.py'
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("Console version built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Console build failed: {e}")
        return False

def clean_build():
    """Clean build directories."""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"Cleaned {dir_name}/")
    
    # Remove .spec files
    for spec_file in Path('.').glob('*.spec'):
        spec_file.unlink()
        print(f"Removed {spec_file}")

def main():
    """Main build function."""
    if len(sys.argv) > 1:
        if sys.argv[1] == 'clean':
            clean_build()
            return
        elif sys.argv[1] == 'console':
            build_console_version()
            return
        elif sys.argv[1] == 'both':
            build_executable()
            build_console_version()
            return
    
    # Default: build windowed version
    success = build_executable()
    
    if success:
        print("\nBuild completed successfully!")
        print("\nFiles created:")
        print("  dist/AntivirusAgent.exe - Main executable")
        print("  dist/data/ - Malware database and whitelist")
        print("  dist/config.json - Configuration file")
        print("  dist/*.bat - Service management batch files")
        print("\nTo install as Windows service:")
        print("  1. Run as Administrator")
        print("  2. Execute: dist/install_service.bat")
        print("\nTo run in foreground mode:")
        print("  Execute: dist/run_foreground.bat")
    else:
        print("\nBuild failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
