"""
Configuration management for the antivirus endpoint agent.
"""

import json
import os
from pathlib import Path

class Config:
    """Configuration manager for the antivirus agent."""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.default_config = {
            "scan_directories": [
                "C:\\Users",
                "C:\\Program Files",
                "C:\\Program Files (x86)",
                "C:\\Windows\\System32"
            ],
            "quarantine_directory": "quarantine",
            "log_directory": "logs",
            "database_file": "data/malware_signatures.json",
            "whitelist_file": "data/whitelist.json",
            "max_file_size_mb": 100,
            "scan_schedule": {
                "daily": "02:00",
                "weekly": "Sunday 03:00"
            },
            "real_time_monitoring": True,
            "heuristic_scanning": True,
            "signature_scanning": True,
            "log_level": "INFO",
            "alert_enabled": True,
            "quarantine_retention_days": 30,
            "excluded_extensions": [
                ".tmp", ".log", ".cache", ".bak"
            ],
            "high_risk_extensions": [
                ".exe", ".scr", ".bat", ".cmd", ".com", ".pif",
                ".vbs", ".js", ".jar", ".dll", ".sys"
            ]
        }
        self.config = self.load_config()
    
    def load_config(self):
        """Load configuration from file or create default."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults for any missing keys
                for key, value in self.default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.default_config.copy()
    
    def save_config(self, config=None):
        """Save configuration to file."""
        if config is None:
            config = self.config
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_file) if os.path.dirname(self.config_file) else ".", exist_ok=True)
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key, default=None):
        """Get configuration value."""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set configuration value."""
        self.config[key] = value
        self.save_config()
    
    def create_directories(self):
        """Create necessary directories."""
        directories = [
            self.get("quarantine_directory"),
            self.get("log_directory"),
            "data"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
