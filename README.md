# Antivirus Endpoint Agent

A comprehensive, open-source antivirus endpoint protection agent for Windows, built with Python 3. Features real-time file system monitoring, signature-based and heuristic malware detection, quarantine management, and scheduled scanning.

## 🚀 Features

### Core Protection
- **Real-time File Monitoring**: Uses `watchdog` library for efficient file system monitoring
- **Signature-based Detection**: Compares file hashes (SHA256/MD5) against malware database
- **Heuristic Analysis**: Detects suspicious files using entropy analysis and API pattern matching
- **Quarantine System**: Secure isolation of infected files with encryption
- **Scheduled Scanning**: Daily and weekly automated scans

### Advanced Capabilities
- **Multi-threaded Scanning**: Concurrent file processing for improved performance
- **Whitelist Management**: Reduces false positives for known safe files
- **Comprehensive Logging**: Detailed audit trail of all security events
- **Alert System**: Pop-up notifications and console alerts for threat detection
- **Windows Service**: Runs as background service or scheduled task

### Management Interface
- **Command-line Interface**: Full control via CLI commands
- **Service Management**: Easy installation and management as Windows service
- **Configuration Management**: JSON-based configuration with runtime updates
- **Quarantine Management**: Restore or permanently delete quarantined files

## 📋 Requirements

- **Operating System**: Windows 10/11 or Windows Server 2016+
- **Python**: Python 3.8 or higher
- **Memory**: Minimum 512MB RAM
- **Storage**: 100MB free space for installation
- **Privileges**: Administrator rights for service installation

## 🛠️ Installation

### Method 1: Quick Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-repo/antivirus-endpoint-agent.git
   cd antivirus-endpoint-agent
   ```

2. **Run the installer** (as Administrator):
   ```bash
   python install_service.py --full
   ```

This will automatically:
- Install all required dependencies
- Create necessary directories
- Install as Windows service
- Create desktop shortcuts
- Run initial system scan

### Method 2: Manual Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Create directories**:
   ```bash
   mkdir logs quarantine data
   ```

3. **Install as Windows service** (as Administrator):
   ```bash
   python service.py install
   ```

4. **Start the service**:
   ```bash
   net start AntivirusEndpointAgent
   ```

### Method 3: Standalone Executable

1. **Build executable**:
   ```bash
   python build_exe.py
   ```

2. **Install from dist folder** (as Administrator):
   ```bash
   cd dist
   install_service.bat
   ```

## 🎯 Usage

### Command Line Interface

#### Scanning Operations
```bash
# Scan a specific directory
python main.py --scan "C:\Users\<USER>\Downloads"

# Scan a single file
python main.py --scan-file "suspicious_file.exe"

# Quick scan of common locations
python main.py --quick-scan

# Full system scan
python main.py --full-scan
```

#### Service Management
```bash
# Check service status
python main.py --status

# Start real-time monitoring
python main.py --start-monitoring

# Stop real-time monitoring
python main.py --stop-monitoring

# Run service in foreground (for debugging)
python main.py --service
```

#### Quarantine Management
```bash
# List quarantined files
python main.py --quarantine-list

# Restore a quarantined file
python main.py --restore q_1234567890

# Permanently delete quarantined file
python main.py --delete-quarantine q_1234567890

# Show quarantine statistics
python main.py --quarantine-stats
```

#### Database Management
```bash
# Update malware database
python main.py --update-db new_signatures.json

# Show database statistics
python main.py --db-stats
```

### Windows Service Management

#### Using Command Line
```bash
# Install service
python service.py install

# Start service
net start AntivirusEndpointAgent

# Stop service
net stop AntivirusEndpointAgent

# Uninstall service
python service.py uninstall
```

#### Using Task Scheduler (Alternative)
```bash
# Install as scheduled task
python service.py task-install

# Start task
python service.py task-start

# Check task status
python service.py task-status

# Remove task
python service.py task-uninstall
```

## ⚙️ Configuration

The agent uses `config.json` for configuration. Key settings include:

```json
{
    "scan_directories": [
        "C:\\Users",
        "C:\\Program Files",
        "C:\\Program Files (x86)",
        "C:\\ProgramData"
    ],
    "real_time_monitoring": true,
    "heuristic_scanning": true,
    "signature_scanning": true,
    "scan_schedule": {
        "daily": "02:00",
        "weekly": "Sunday 03:00"
    },
    "quarantine_retention_days": 30,
    "max_file_size_mb": 100,
    "log_level": "INFO"
}
```

### Configuration Options

- **scan_directories**: Directories to monitor and scan
- **real_time_monitoring**: Enable/disable real-time file monitoring
- **heuristic_scanning**: Enable/disable heuristic analysis
- **signature_scanning**: Enable/disable signature-based detection
- **scan_schedule**: Scheduled scan times
- **quarantine_retention_days**: How long to keep quarantined files
- **max_file_size_mb**: Maximum file size to scan
- **excluded_extensions**: File extensions to skip
- **high_risk_extensions**: Extensions that increase threat score

## 🗂️ File Structure

```
antivirus-endpoint-agent/
├── main.py                 # Main CLI interface
├── config.py              # Configuration management
├── scanner.py             # File scanning engine
├── monitor.py             # Real-time monitoring
├── quarantine.py          # Quarantine management
├── database.py            # Malware signature database
├── scheduler.py           # Scheduled scan management
├── alerts.py              # Alert and notification system
├── logger.py              # Logging system
├── service.py             # Windows service implementation
├── install_service.py     # Installation script
├── build_exe.py           # PyInstaller build script
├── requirements.txt       # Python dependencies
├── config.json            # Default configuration
├── data/
│   ├── malware_signatures.json  # Malware signature database
│   └── whitelist.json           # Whitelist for false positive reduction
├── logs/                  # Log files directory
└── quarantine/           # Quarantined files storage
```

## 🔍 Detection Methods

### Signature-based Detection
- Compares file SHA256 and MD5 hashes against known malware database
- Includes signatures for major malware families
- Supports database updates from external sources

### Heuristic Analysis
- **Entropy Analysis**: Detects packed/encrypted files (entropy > 7.0)
- **API Pattern Matching**: Identifies suspicious Windows API usage
- **File Location Analysis**: Flags executables in temporary directories
- **Extension Risk Assessment**: Evaluates file extensions for risk level

### Risk Scoring
- **High Risk (70+)**: Automatic quarantine
- **Medium Risk (40-69)**: Quarantine with user notification
- **Low Risk (20-39)**: Log only, no action
- **Clean (0-19)**: No threat detected

## 📊 Logging and Monitoring

### Log Files
- **antivirus.log**: General application events
- **scan_results.log**: Detailed scan results
- **threats.log**: Threat detection events
- **quarantine.log**: Quarantine operations

### Log Levels
- **DEBUG**: Detailed debugging information
- **INFO**: General information messages
- **WARNING**: Warning messages and threat alerts
- **ERROR**: Error messages and failures

## 🚨 Alert System

### Notification Methods
1. **Windows Balloon Notifications**: Native Windows notifications
2. **Cross-platform Notifications**: Using plyer library
3. **Console Alerts**: Fallback text-based alerts
4. **Audio Alerts**: Optional sound notifications

### Alert Types
- **Threat Detection**: Immediate alerts for malware detection
- **Scan Completion**: Summary of scan results
- **Service Status**: Service start/stop notifications
- **System Errors**: Critical error notifications

## 🔒 Security Features

### Quarantine Security
- **File Encryption**: Quarantined files are encrypted using Fernet (AES 128)
- **Metadata Protection**: Separate encrypted metadata storage
- **Access Control**: Restricted access to quarantine directory
- **Integrity Verification**: Hash verification for quarantined files

### Whitelist Protection
- **Path-based Whitelisting**: Exclude trusted directories
- **Hash-based Whitelisting**: Exclude known safe files
- **Process Whitelisting**: Exclude trusted system processes
- **Dynamic Updates**: Runtime whitelist modifications

## 🧪 Testing

### Test Malware Detection
```bash
# Create test file with suspicious content
echo "CreateRemoteThread" > test_malware.txt
python main.py --scan-file test_malware.txt
```

### Test Real-time Monitoring
1. Start the service: `python main.py --service`
2. Create a file in monitored directory
3. Check logs for detection events

### Test Quarantine System
```bash
# List quarantined files
python main.py --quarantine-list

# Test restore functionality
python main.py --restore q_1234567890
```

## 📦 Building Standalone Executable

### Build Process
```bash
# Install PyInstaller
pip install pyinstaller

# Build executable
python build_exe.py

# Build console version for debugging
python build_exe.py console

# Clean build files
python build_exe.py clean
```

### Distribution
The built executable includes:
- **AntivirusAgent.exe**: Main executable
- **data/**: Malware database and whitelist
- **config.json**: Configuration file
- **Batch files**: Service management scripts

## 🔧 Troubleshooting

### Common Issues

#### Service Installation Fails
- **Solution**: Run as Administrator
- **Check**: Windows service dependencies installed (`pip install pywin32`)

#### Real-time Monitoring Not Working
- **Check**: Monitored directories exist and are accessible
- **Verify**: No permission issues with target directories
- **Review**: Excluded extensions configuration

#### High CPU Usage
- **Adjust**: `cpu_usage_limit` in configuration
- **Enable**: `scan_throttle_enabled` setting
- **Reduce**: Number of monitored directories

#### False Positives
- **Update**: Whitelist with safe file hashes
- **Adjust**: Heuristic detection thresholds
- **Exclude**: Trusted directories from monitoring

### Debug Mode
```bash
# Run with debug logging
python main.py --log-level DEBUG --service

# Check service status
python service.py status

# View recent logs
type logs\antivirus.log
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This antivirus solution is provided for educational and research purposes. While it implements industry-standard detection techniques, it should not be considered a replacement for commercial antivirus solutions in production environments. Always maintain updated commercial antivirus software for comprehensive protection.

## 🆘 Support

For issues, questions, or contributions:
- **GitHub Issues**: Report bugs and request features
- **Documentation**: Check this README and inline code comments
- **Logs**: Review log files for troubleshooting information

---

**Built with ❤️ using Python 3 and open-source libraries**
