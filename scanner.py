"""
File scanning engine with signature-based and heuristic detection.
"""

import os
import hashlib
import math
import re
import time
from pathlib import Path
from collections import Counter
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

class FileScanner:
    """Advanced file scanner with multiple detection methods."""
    
    def __init__(self, database, logger, config):
        self.database = database
        self.logger = logger
        self.config = config
        self.max_file_size = config.get("max_file_size_mb", 100) * 1024 * 1024
        self.excluded_extensions = config.get("excluded_extensions", [])
        self.high_risk_extensions = config.get("high_risk_extensions", [])
        
        # Suspicious patterns for heuristic detection
        self.suspicious_patterns = [
            rb'CreateRemoteThread',
            rb'VirtualAllocEx',
            rb'WriteProcessMemory',
            rb'SetWindowsHookEx',
            rb'GetProcAddress',
            rb'LoadLibrary',
            rb'WinExec',
            rb'ShellExecute',
            rb'CreateProcess',
            rb'RegSetValue',
            rb'RegCreateKey',
            rb'CryptEncrypt',
            rb'CryptDecrypt',
            rb'InternetOpen',
            rb'InternetConnect',
            rb'HttpSendRequest',
            rb'URLDownloadToFile',
            rb'CreateService',
            rb'StartService',
            rb'DeleteFile',
            rb'MoveFile',
            rb'CopyFile'
        ]
        
        # Entropy threshold for packed/encrypted files
        self.entropy_threshold = 7.0
    
    def calculate_file_hash(self, file_path, algorithms=['md5', 'sha256']):
        """Calculate file hashes."""
        hashes = {}
        
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                
                for algorithm in algorithms:
                    if algorithm == 'md5':
                        hashes['md5'] = hashlib.md5(content).hexdigest()
                    elif algorithm == 'sha256':
                        hashes['sha256'] = hashlib.sha256(content).hexdigest()
                        
        except Exception as e:
            self.logger.log_app_event("error", f"Error calculating hash for {file_path}: {e}")
            
        return hashes
    
    def calculate_entropy(self, data):
        """Calculate Shannon entropy of data."""
        if not data:
            return 0
        
        # Count byte frequencies
        byte_counts = Counter(data)
        data_len = len(data)
        
        # Calculate entropy
        entropy = 0
        for count in byte_counts.values():
            probability = count / data_len
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def signature_scan(self, file_path):
        """Perform signature-based scanning."""
        try:
            # Check if file is whitelisted
            if self.database.is_whitelisted(file_path):
                return {
                    'threat_detected': False,
                    'scan_type': 'signature',
                    'result': 'whitelisted'
                }
            
            # Calculate file hashes
            hashes = self.calculate_file_hash(file_path)
            
            if not hashes:
                return {
                    'threat_detected': False,
                    'scan_type': 'signature',
                    'result': 'error_calculating_hash'
                }
            
            # Check against malware database
            for hash_type, hash_value in hashes.items():
                is_malware, signature = self.database.is_malware(hash_value, hash_type)
                if is_malware:
                    return {
                        'threat_detected': True,
                        'scan_type': 'signature',
                        'result': 'malware_detected',
                        'threat_name': signature['name'],
                        'threat_type': signature['type'],
                        'severity': signature['severity'],
                        'hash': hash_value,
                        'hash_type': hash_type
                    }
            
            return {
                'threat_detected': False,
                'scan_type': 'signature',
                'result': 'clean'
            }
            
        except Exception as e:
            self.logger.log_app_event("error", f"Error in signature scan for {file_path}: {e}")
            return {
                'threat_detected': False,
                'scan_type': 'signature',
                'result': 'scan_error',
                'error': str(e)
            }
    
    def heuristic_scan(self, file_path):
        """Perform heuristic analysis."""
        try:
            file_size = os.path.getsize(file_path)
            
            # Skip large files
            if file_size > self.max_file_size:
                return {
                    'threat_detected': False,
                    'scan_type': 'heuristic',
                    'result': 'file_too_large'
                }
            
            # Check file extension
            file_ext = Path(file_path).suffix.lower()
            risk_score = 0
            suspicious_indicators = []
            
            # High-risk extension check
            if file_ext in self.high_risk_extensions:
                risk_score += 30
                suspicious_indicators.append(f"High-risk extension: {file_ext}")
            
            # Read file content for analysis
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # Entropy analysis
            entropy = self.calculate_entropy(content)
            if entropy > self.entropy_threshold:
                risk_score += 25
                suspicious_indicators.append(f"High entropy: {entropy:.2f}")
            
            # Suspicious API pattern detection
            suspicious_api_count = 0
            for pattern in self.suspicious_patterns:
                if pattern in content:
                    suspicious_api_count += 1
            
            if suspicious_api_count > 5:
                risk_score += 40
                suspicious_indicators.append(f"Multiple suspicious APIs: {suspicious_api_count}")
            elif suspicious_api_count > 2:
                risk_score += 20
                suspicious_indicators.append(f"Some suspicious APIs: {suspicious_api_count}")
            
            # Check for executable in non-executable location
            if file_ext in ['.exe', '.scr', '.com'] and 'temp' in file_path.lower():
                risk_score += 25
                suspicious_indicators.append("Executable in temp directory")
            
            # Determine threat level
            if risk_score >= 70:
                threat_level = "high"
                threat_detected = True
            elif risk_score >= 40:
                threat_level = "medium"
                threat_detected = True
            elif risk_score >= 20:
                threat_level = "low"
                threat_detected = False  # Don't quarantine low-risk files
            else:
                threat_level = "clean"
                threat_detected = False
            
            return {
                'threat_detected': threat_detected,
                'scan_type': 'heuristic',
                'result': threat_level,
                'risk_score': risk_score,
                'indicators': suspicious_indicators,
                'entropy': entropy,
                'file_size': file_size
            }
            
        except Exception as e:
            self.logger.log_app_event("error", f"Error in heuristic scan for {file_path}: {e}")
            return {
                'threat_detected': False,
                'scan_type': 'heuristic',
                'result': 'scan_error',
                'error': str(e)
            }
    
    def scan_file(self, file_path):
        """Comprehensive file scan combining signature and heuristic methods."""
        file_path = Path(file_path)
        
        # Skip if file doesn't exist
        if not file_path.exists():
            return None
        
        # Skip excluded extensions
        if file_path.suffix.lower() in self.excluded_extensions:
            return None
        
        scan_results = {
            'file_path': str(file_path),
            'scan_timestamp': time.time(),
            'signature_result': None,
            'heuristic_result': None,
            'final_verdict': 'clean'
        }
        
        # Signature-based scan
        if self.config.get("signature_scanning", True):
            scan_results['signature_result'] = self.signature_scan(file_path)
        
        # Heuristic scan
        if self.config.get("heuristic_scanning", True):
            scan_results['heuristic_result'] = self.heuristic_scan(file_path)
        
        # Determine final verdict
        threat_detected = False
        threat_info = {}
        
        # Check signature scan result
        if scan_results['signature_result'] and scan_results['signature_result']['threat_detected']:
            threat_detected = True
            threat_info = scan_results['signature_result']
            scan_results['final_verdict'] = 'malware'
        
        # Check heuristic scan result
        elif scan_results['heuristic_result'] and scan_results['heuristic_result']['threat_detected']:
            threat_detected = True
            threat_info = scan_results['heuristic_result']
            scan_results['final_verdict'] = 'suspicious'
        
        # Log scan result
        self.logger.log_scan_result(
            file_path=file_path,
            scan_type="comprehensive",
            result=scan_results['final_verdict'],
            threat_level=threat_info.get('severity') or threat_info.get('result'),
            details=threat_info
        )
        
        return scan_results
    
    def scan_directory(self, directory_path, recursive=True, max_workers=4):
        """Scan entire directory with threading support."""
        directory_path = Path(directory_path)
        
        if not directory_path.exists():
            self.logger.log_app_event("error", f"Directory does not exist: {directory_path}")
            return []
        
        self.logger.log_app_event("info", f"Starting directory scan: {directory_path}")
        
        # Collect files to scan
        files_to_scan = []
        
        try:
            if recursive:
                for file_path in directory_path.rglob('*'):
                    if file_path.is_file():
                        files_to_scan.append(file_path)
            else:
                for file_path in directory_path.iterdir():
                    if file_path.is_file():
                        files_to_scan.append(file_path)
        except Exception as e:
            self.logger.log_app_event("error", f"Error collecting files from {directory_path}: {e}")
            return []
        
        self.logger.log_app_event("info", f"Found {len(files_to_scan)} files to scan")
        
        # Scan files with threading
        scan_results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(self.scan_file, file_path): file_path 
                             for file_path in files_to_scan}
            
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    if result:
                        scan_results.append(result)
                except Exception as e:
                    self.logger.log_app_event("error", f"Error scanning {file_path}: {e}")
        
        self.logger.log_app_event("info", f"Directory scan completed. Scanned {len(scan_results)} files")
        return scan_results
