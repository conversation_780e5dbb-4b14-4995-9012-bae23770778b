"""
Real-time file system monitoring for the antivirus endpoint agent.
"""

import os
import time
import threading
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class AntivirusFileHandler(FileSystemEventHandler):
    """File system event handler for real-time monitoring."""
    
    def __init__(self, scanner, quarantine_manager, logger, config):
        super().__init__()
        self.scanner = scanner
        self.quarantine_manager = quarantine_manager
        self.logger = logger
        self.config = config
        self.excluded_extensions = config.get("excluded_extensions", [])
        
        # Rate limiting to prevent excessive scanning
        self.scan_queue = {}
        self.scan_delay = 2  # seconds
        self.queue_lock = threading.Lock()
        
        # Start queue processor
        self.queue_processor = threading.Thread(target=self._process_scan_queue, daemon=True)
        self.queue_processor.start()
    
    def on_created(self, event):
        """Handle file creation events."""
        if not event.is_directory:
            self._queue_file_for_scan(event.src_path, "created")
    
    def on_modified(self, event):
        """Handle file modification events."""
        if not event.is_directory:
            self._queue_file_for_scan(event.src_path, "modified")
    
    def on_moved(self, event):
        """Handle file move events."""
        if not event.is_directory:
            self._queue_file_for_scan(event.dest_path, "moved")
    
    def _queue_file_for_scan(self, file_path, event_type):
        """Queue file for delayed scanning to avoid excessive scans."""
        file_path = Path(file_path)
        
        # Skip excluded extensions
        if file_path.suffix.lower() in self.excluded_extensions:
            return
        
        # Skip temporary files and system files
        if any(pattern in file_path.name.lower() for pattern in ['~', '.tmp', '.temp', '.lock']):
            return
        
        with self.queue_lock:
            # Add to queue with current timestamp
            self.scan_queue[str(file_path)] = {
                'timestamp': time.time(),
                'event_type': event_type
            }
    
    def _process_scan_queue(self):
        """Process queued files for scanning."""
        while True:
            try:
                current_time = time.time()
                files_to_scan = []
                
                with self.queue_lock:
                    # Find files ready for scanning
                    for file_path, info in list(self.scan_queue.items()):
                        if current_time - info['timestamp'] >= self.scan_delay:
                            files_to_scan.append((file_path, info['event_type']))
                            del self.scan_queue[file_path]
                
                # Scan files
                for file_path, event_type in files_to_scan:
                    self._scan_file_realtime(file_path, event_type)
                
                time.sleep(1)  # Check queue every second
                
            except Exception as e:
                self.logger.log_app_event("error", f"Error in scan queue processor: {e}")
                time.sleep(5)  # Wait before retrying
    
    def _scan_file_realtime(self, file_path, event_type):
        """Perform real-time scan on file."""
        try:
            file_path = Path(file_path)
            
            # Check if file still exists
            if not file_path.exists():
                return
            
            # Skip if file is being written to
            try:
                # Try to open file exclusively to check if it's being written
                with open(file_path, 'rb') as f:
                    pass
            except (PermissionError, OSError):
                # File is likely being written to, skip for now
                return
            
            self.logger.log_app_event(
                "info", f"Real-time scanning file: {file_path} (event: {event_type})"
            )
            
            # Perform scan
            scan_result = self.scanner.scan_file(file_path)
            
            if scan_result and scan_result['final_verdict'] in ['malware', 'suspicious']:
                # Threat detected - quarantine file
                threat_info = scan_result.get('signature_result') or scan_result.get('heuristic_result')
                
                self.logger.log_threat_detection(
                    file_path=file_path,
                    threat_type=threat_info.get('threat_type', 'unknown'),
                    threat_name=threat_info.get('threat_name', 'Heuristic Detection'),
                    action_taken="quarantined",
                    details=threat_info
                )
                
                # Quarantine the file
                success, result = self.quarantine_manager.quarantine_file(file_path, threat_info)
                
                if success:
                    self.logger.log_app_event(
                        "warning", f"File quarantined: {file_path} -> {result}"
                    )
                    
                    # Send alert if enabled
                    if self.config.get("alert_enabled", True):
                        self._send_threat_alert(file_path, threat_info)
                else:
                    self.logger.log_app_event(
                        "error", f"Failed to quarantine file {file_path}: {result}"
                    )
            
        except Exception as e:
            self.logger.log_app_event("error", f"Error in real-time scan of {file_path}: {e}")
    
    def _send_threat_alert(self, file_path, threat_info):
        """Send threat detection alert."""
        try:
            from alerts import AlertManager
            alert_manager = AlertManager(self.logger)
            alert_manager.send_threat_alert(file_path, threat_info)
        except ImportError:
            # Fallback to simple console alert
            print(f"\n*** THREAT DETECTED ***")
            print(f"File: {file_path}")
            print(f"Threat: {threat_info.get('threat_name', 'Unknown')}")
            print(f"Type: {threat_info.get('threat_type', 'Unknown')}")
            print(f"Action: File has been quarantined")
            print("*" * 50)

class RealTimeMonitor:
    """Real-time file system monitor."""
    
    def __init__(self, scanner, quarantine_manager, logger, config):
        self.scanner = scanner
        self.quarantine_manager = quarantine_manager
        self.logger = logger
        self.config = config
        self.observer = None
        self.is_running = False
        
        # Directories to monitor
        self.monitor_directories = config.get("scan_directories", [])
        
        # Event handler
        self.event_handler = AntivirusFileHandler(
            scanner, quarantine_manager, logger, config
        )
    
    def start_monitoring(self):
        """Start real-time monitoring."""
        if self.is_running:
            self.logger.log_app_event("warning", "Real-time monitoring is already running")
            return False
        
        try:
            self.observer = Observer()
            
            # Add watchers for each directory
            for directory in self.monitor_directories:
                if os.path.exists(directory):
                    self.observer.schedule(
                        self.event_handler,
                        directory,
                        recursive=True
                    )
                    self.logger.log_app_event("info", f"Monitoring directory: {directory}")
                else:
                    self.logger.log_app_event("warning", f"Directory not found: {directory}")
            
            self.observer.start()
            self.is_running = True
            
            self.logger.log_app_event("info", "Real-time monitoring started")
            return True
            
        except Exception as e:
            self.logger.log_app_event("error", f"Error starting real-time monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop real-time monitoring."""
        if not self.is_running:
            return False
        
        try:
            if self.observer:
                self.observer.stop()
                self.observer.join(timeout=10)
            
            self.is_running = False
            self.logger.log_app_event("info", "Real-time monitoring stopped")
            return True
            
        except Exception as e:
            self.logger.log_app_event("error", f"Error stopping real-time monitoring: {e}")
            return False
    
    def is_monitoring(self):
        """Check if monitoring is active."""
        return self.is_running and self.observer and self.observer.is_alive()
    
    def add_directory(self, directory):
        """Add directory to monitoring."""
        if not os.path.exists(directory):
            return False, "Directory does not exist"
        
        if directory in self.monitor_directories:
            return False, "Directory already being monitored"
        
        try:
            if self.is_running and self.observer:
                self.observer.schedule(
                    self.event_handler,
                    directory,
                    recursive=True
                )
            
            self.monitor_directories.append(directory)
            self.config.set("scan_directories", self.monitor_directories)
            
            self.logger.log_app_event("info", f"Added directory to monitoring: {directory}")
            return True, "Directory added successfully"
            
        except Exception as e:
            error_msg = f"Error adding directory to monitoring: {e}"
            self.logger.log_app_event("error", error_msg)
            return False, error_msg
    
    def remove_directory(self, directory):
        """Remove directory from monitoring."""
        if directory not in self.monitor_directories:
            return False, "Directory not being monitored"
        
        try:
            # Note: watchdog doesn't support removing individual watches easily
            # So we restart the observer with updated directories
            was_running = self.is_running
            
            if was_running:
                self.stop_monitoring()
            
            self.monitor_directories.remove(directory)
            self.config.set("scan_directories", self.monitor_directories)
            
            if was_running:
                self.start_monitoring()
            
            self.logger.log_app_event("info", f"Removed directory from monitoring: {directory}")
            return True, "Directory removed successfully"
            
        except Exception as e:
            error_msg = f"Error removing directory from monitoring: {e}"
            self.logger.log_app_event("error", error_msg)
            return False, error_msg
    
    def get_monitoring_status(self):
        """Get current monitoring status."""
        return {
            "is_running": self.is_running,
            "monitored_directories": self.monitor_directories.copy(),
            "observer_alive": self.observer.is_alive() if self.observer else False
        }
