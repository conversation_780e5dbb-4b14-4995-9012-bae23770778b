"""
Test script for the antivirus endpoint agent.
"""

import os
import sys
import tempfile
import time
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import AntivirusAgent

def create_test_files():
    """Create test files for scanning."""
    test_dir = Path("test_files")
    test_dir.mkdir(exist_ok=True)
    
    # Clean file
    clean_file = test_dir / "clean_file.txt"
    with open(clean_file, 'w') as f:
        f.write("This is a clean test file with normal content.")
    
    # Suspicious file (high entropy)
    suspicious_file = test_dir / "suspicious_file.exe"
    with open(suspicious_file, 'wb') as f:
        # Create high-entropy content
        import random
        random_data = bytes([random.randint(0, 255) for _ in range(1024)])
        f.write(random_data)
    
    # File with suspicious API patterns
    api_file = test_dir / "api_test.exe"
    with open(api_file, 'wb') as f:
        suspicious_content = b"CreateRemoteThread" + b"VirtualAllocEx" + b"WriteProcessMemory"
        f.write(suspicious_content + b"\x00" * 1000)
    
    # Test malware file (matches signature)
    malware_file = test_dir / "test_malware.exe"
    with open(malware_file, 'w') as f:
        f.write("hello")  # This will generate hash that matches our test signature
    
    print(f"Created test files in {test_dir}")
    return test_dir

def test_file_scanning():
    """Test file scanning functionality."""
    print("\n=== Testing File Scanning ===")
    
    agent = AntivirusAgent()
    test_dir = create_test_files()
    
    # Test individual file scanning
    for file_path in test_dir.iterdir():
        if file_path.is_file():
            print(f"\nScanning: {file_path.name}")
            result = agent.scanner.scan_file(file_path)
            
            if result:
                print(f"  Verdict: {result['final_verdict']}")
                
                if result['signature_result']:
                    sig_result = result['signature_result']
                    print(f"  Signature: {sig_result['result']}")
                    if sig_result.get('threat_detected'):
                        print(f"  Threat: {sig_result.get('threat_name', 'Unknown')}")
                
                if result['heuristic_result']:
                    heur_result = result['heuristic_result']
                    print(f"  Heuristic: {heur_result['result']}")
                    if heur_result.get('risk_score'):
                        print(f"  Risk Score: {heur_result['risk_score']}")
                    if heur_result.get('indicators'):
                        print(f"  Indicators: {', '.join(heur_result['indicators'])}")
    
    return test_dir

def test_directory_scanning():
    """Test directory scanning functionality."""
    print("\n=== Testing Directory Scanning ===")
    
    agent = AntivirusAgent()
    test_dir = create_test_files()
    
    print(f"Scanning directory: {test_dir}")
    results = agent.scanner.scan_directory(test_dir)
    
    print(f"Scanned {len(results)} files")
    
    threats_found = 0
    for result in results:
        if result['final_verdict'] in ['malware', 'suspicious']:
            threats_found += 1
            print(f"  Threat: {result['file_path']} - {result['final_verdict']}")
    
    print(f"Total threats found: {threats_found}")
    return results

def test_quarantine_system():
    """Test quarantine functionality."""
    print("\n=== Testing Quarantine System ===")
    
    agent = AntivirusAgent()
    
    # Create a test file to quarantine
    test_file = Path("test_quarantine_file.txt")
    with open(test_file, 'w') as f:
        f.write("This file will be quarantined for testing")
    
    print(f"Created test file: {test_file}")
    
    # Quarantine the file
    threat_info = {
        'threat_name': 'Test.Threat',
        'threat_type': 'test',
        'severity': 'medium'
    }
    
    success, quarantine_id = agent.quarantine_manager.quarantine_file(test_file, threat_info)
    
    if success:
        print(f"File quarantined successfully: {quarantine_id}")
        
        # List quarantined files
        quarantined_files = agent.quarantine_manager.list_quarantined_files()
        print(f"Quarantined files: {len(quarantined_files)}")
        
        # Test restore
        restore_path = Path("restored_test_file.txt")
        success, result = agent.quarantine_manager.restore_file(quarantine_id, restore_path)
        
        if success:
            print(f"File restored successfully to: {result}")
            
            # Verify restored file
            if restore_path.exists():
                with open(restore_path, 'r') as f:
                    content = f.read()
                print(f"Restored file content: {content[:50]}...")
                
                # Clean up
                restore_path.unlink()
            
            # Re-quarantine for cleanup test
            agent.quarantine_manager.quarantine_file(restore_path, threat_info)
        else:
            print(f"Failed to restore file: {result}")
        
        # Test permanent deletion
        success, result = agent.quarantine_manager.delete_quarantined_file(quarantine_id)
        if success:
            print("File permanently deleted from quarantine")
        else:
            print(f"Failed to delete quarantined file: {result}")
    
    else:
        print(f"Failed to quarantine file: {quarantine_id}")
    
    # Clean up
    if test_file.exists():
        test_file.unlink()

def test_real_time_monitoring():
    """Test real-time monitoring (basic test)."""
    print("\n=== Testing Real-time Monitoring ===")
    
    agent = AntivirusAgent()
    
    # Create a temporary directory for monitoring
    test_dir = Path("monitor_test")
    test_dir.mkdir(exist_ok=True)
    
    # Add directory to monitoring
    success, message = agent.monitor.add_directory(str(test_dir))
    print(f"Added directory to monitoring: {success} - {message}")
    
    # Start monitoring
    if agent.monitor.start_monitoring():
        print("Real-time monitoring started")
        
        # Create a test file
        test_file = test_dir / "monitored_file.txt"
        print(f"Creating file: {test_file}")
        
        with open(test_file, 'w') as f:
            f.write("This file should be detected by real-time monitoring")
        
        # Wait a moment for monitoring to detect
        print("Waiting for monitoring to detect file...")
        time.sleep(5)
        
        # Stop monitoring
        agent.monitor.stop_monitoring()
        print("Real-time monitoring stopped")
        
        # Clean up
        test_file.unlink()
        test_dir.rmdir()
    
    else:
        print("Failed to start real-time monitoring")

def test_database_operations():
    """Test database operations."""
    print("\n=== Testing Database Operations ===")
    
    agent = AntivirusAgent()
    
    # Test signature count
    sig_count = agent.database.get_signature_count()
    print(f"Total signatures in database: {sig_count}")
    
    # Test hash lookup
    test_hash = "2c26b46b68ffc68ff99b453c1d30413413422d706483bfa0f98a5e886266e7ae"
    is_malware, signature = agent.database.is_malware(test_hash, "sha256")
    
    if is_malware:
        print(f"Hash {test_hash[:16]}... is malware: {signature['name']}")
    else:
        print(f"Hash {test_hash[:16]}... is not in malware database")
    
    # Test whitelist
    test_path = "C:\\Windows\\System32\\notepad.exe"
    is_whitelisted = agent.database.is_whitelisted(test_path)
    print(f"Path {test_path} is whitelisted: {is_whitelisted}")
    
    # Add new signature
    agent.database.add_signature(
        hash_md5="test_md5_hash",
        hash_sha256="test_sha256_hash",
        name="Test.Signature",
        malware_type="test",
        severity="low",
        description="Test signature for unit testing"
    )
    
    new_count = agent.database.get_signature_count()
    print(f"Signatures after adding test signature: {new_count}")

def test_configuration():
    """Test configuration management."""
    print("\n=== Testing Configuration ===")
    
    from config import Config
    
    config = Config()
    
    # Test getting values
    scan_dirs = config.get("scan_directories")
    print(f"Scan directories: {len(scan_dirs)} configured")
    
    log_level = config.get("log_level")
    print(f"Log level: {log_level}")
    
    # Test setting values
    config.set("test_setting", "test_value")
    test_value = config.get("test_setting")
    print(f"Test setting: {test_value}")

def test_logging():
    """Test logging functionality."""
    print("\n=== Testing Logging ===")
    
    from logger import AntivirusLogger
    
    logger = AntivirusLogger()
    
    # Test different log types
    logger.log_app_event("info", "Test application event")
    logger.log_scan_result("test_file.txt", "test", "clean")
    logger.log_threat_detection("malware.exe", "virus", "Test.Virus", "quarantined")
    logger.log_quarantine_action("quarantine", "malware.exe", "quarantine/q_123", True)
    
    print("Logging tests completed - check log files")

def run_all_tests():
    """Run all tests."""
    print("Starting Antivirus Agent Tests")
    print("=" * 50)
    
    try:
        # Basic functionality tests
        test_configuration()
        test_logging()
        test_database_operations()
        
        # Core scanning tests
        test_file_scanning()
        test_directory_scanning()
        
        # Advanced functionality tests
        test_quarantine_system()
        test_real_time_monitoring()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test files
        cleanup_test_files()

def cleanup_test_files():
    """Clean up test files and directories."""
    test_paths = [
        "test_files",
        "monitor_test",
        "test_quarantine_file.txt",
        "restored_test_file.txt"
    ]
    
    for path in test_paths:
        path_obj = Path(path)
        try:
            if path_obj.is_file():
                path_obj.unlink()
            elif path_obj.is_dir():
                import shutil
                shutil.rmtree(path_obj)
        except:
            pass  # Ignore cleanup errors

def main():
    """Main test function."""
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        
        test_functions = {
            'scan': test_file_scanning,
            'directory': test_directory_scanning,
            'quarantine': test_quarantine_system,
            'monitor': test_real_time_monitoring,
            'database': test_database_operations,
            'config': test_configuration,
            'logging': test_logging
        }
        
        if test_name in test_functions:
            print(f"Running {test_name} test...")
            test_functions[test_name]()
        else:
            print(f"Unknown test: {test_name}")
            print(f"Available tests: {', '.join(test_functions.keys())}")
    else:
        run_all_tests()

if __name__ == "__main__":
    main()
