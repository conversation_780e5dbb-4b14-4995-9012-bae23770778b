"""
Quarantine management system for the antivirus endpoint agent.
"""

import os
import shutil
import json
import time
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import threading
from cryptography.fernet import Fernet

class QuarantineManager:
    """Manages quarantine operations for infected files."""
    
    def __init__(self, quarantine_dir="quarantine", logger=None, config=None):
        self.quarantine_dir = Path(quarantine_dir)
        self.logger = logger
        self.config = config
        self.retention_days = config.get("quarantine_retention_days", 30) if config else 30
        
        # Create quarantine directory structure
        self.quarantine_dir.mkdir(exist_ok=True)
        self.metadata_dir = self.quarantine_dir / "metadata"
        self.metadata_dir.mkdir(exist_ok=True)
        
        # Generate encryption key for quarantined files
        self.key_file = self.quarantine_dir / "quarantine.key"
        self.encryption_key = self._get_or_create_key()
        self.cipher = Fernet(self.encryption_key)
        
        # Lock for thread safety
        self.lock = threading.Lock()
    
    def _get_or_create_key(self):
        """Get existing encryption key or create new one."""
        if self.key_file.exists():
            try:
                with open(self.key_file, 'rb') as f:
                    return f.read()
            except Exception as e:
                if self.logger:
                    self.logger.log_app_event("error", f"Error reading encryption key: {e}")
        
        # Create new key
        key = Fernet.generate_key()
        try:
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # Make key file read-only
            os.chmod(self.key_file, 0o400)
        except Exception as e:
            if self.logger:
                self.logger.log_app_event("error", f"Error saving encryption key: {e}")
        
        return key
    
    def _generate_quarantine_id(self):
        """Generate unique quarantine ID."""
        timestamp = str(int(time.time() * 1000))
        return f"q_{timestamp}"
    
    def quarantine_file(self, file_path, threat_info=None):
        """Move file to quarantine with encryption."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            if self.logger:
                self.logger.log_quarantine_action(
                    "quarantine", file_path, success=False, 
                    error="File does not exist"
                )
            return False, "File does not exist"
        
        with self.lock:
            try:
                # Generate quarantine ID
                quarantine_id = self._generate_quarantine_id()
                quarantine_file_path = self.quarantine_dir / f"{quarantine_id}.qfile"
                metadata_file_path = self.metadata_dir / f"{quarantine_id}.json"
                
                # Read and encrypt file content
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                
                encrypted_content = self.cipher.encrypt(file_content)
                
                # Save encrypted file
                with open(quarantine_file_path, 'wb') as f:
                    f.write(encrypted_content)
                
                # Create metadata
                metadata = {
                    "quarantine_id": quarantine_id,
                    "original_path": str(file_path.absolute()),
                    "original_name": file_path.name,
                    "original_size": len(file_content),
                    "quarantine_date": datetime.now().isoformat(),
                    "threat_info": threat_info or {},
                    "file_hash": self._calculate_hash(file_content),
                    "status": "quarantined"
                }
                
                # Save metadata
                with open(metadata_file_path, 'w') as f:
                    json.dump(metadata, f, indent=4)
                
                # Remove original file
                file_path.unlink()
                
                if self.logger:
                    self.logger.log_quarantine_action(
                        "quarantine", file_path, quarantine_file_path, 
                        success=True
                    )
                
                return True, quarantine_id
                
            except Exception as e:
                error_msg = f"Error quarantining file: {e}"
                if self.logger:
                    self.logger.log_quarantine_action(
                        "quarantine", file_path, success=False, error=error_msg
                    )
                return False, error_msg
    
    def restore_file(self, quarantine_id, restore_path=None):
        """Restore file from quarantine."""
        with self.lock:
            try:
                metadata_file = self.metadata_dir / f"{quarantine_id}.json"
                quarantine_file = self.quarantine_dir / f"{quarantine_id}.qfile"
                
                if not metadata_file.exists() or not quarantine_file.exists():
                    return False, "Quarantined file not found"
                
                # Load metadata
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                # Determine restore path
                if restore_path is None:
                    restore_path = Path(metadata["original_path"])
                else:
                    restore_path = Path(restore_path)
                
                # Check if restore path already exists
                if restore_path.exists():
                    return False, "Restore path already exists"
                
                # Create parent directories if needed
                restore_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Read and decrypt file
                with open(quarantine_file, 'rb') as f:
                    encrypted_content = f.read()
                
                decrypted_content = self.cipher.decrypt(encrypted_content)
                
                # Restore file
                with open(restore_path, 'wb') as f:
                    f.write(decrypted_content)
                
                # Update metadata
                metadata["status"] = "restored"
                metadata["restore_date"] = datetime.now().isoformat()
                metadata["restore_path"] = str(restore_path.absolute())
                
                with open(metadata_file, 'w') as f:
                    json.dump(metadata, f, indent=4)
                
                if self.logger:
                    self.logger.log_quarantine_action(
                        "restore", restore_path, quarantine_file, success=True
                    )
                
                return True, str(restore_path)
                
            except Exception as e:
                error_msg = f"Error restoring file: {e}"
                if self.logger:
                    self.logger.log_quarantine_action(
                        "restore", f"quarantine_id:{quarantine_id}", 
                        success=False, error=error_msg
                    )
                return False, error_msg
    
    def delete_quarantined_file(self, quarantine_id):
        """Permanently delete quarantined file."""
        with self.lock:
            try:
                metadata_file = self.metadata_dir / f"{quarantine_id}.json"
                quarantine_file = self.quarantine_dir / f"{quarantine_id}.qfile"
                
                # Remove files
                if quarantine_file.exists():
                    quarantine_file.unlink()
                
                if metadata_file.exists():
                    metadata_file.unlink()
                
                if self.logger:
                    self.logger.log_quarantine_action(
                        "delete", f"quarantine_id:{quarantine_id}", 
                        success=True
                    )
                
                return True, "File permanently deleted"
                
            except Exception as e:
                error_msg = f"Error deleting quarantined file: {e}"
                if self.logger:
                    self.logger.log_quarantine_action(
                        "delete", f"quarantine_id:{quarantine_id}", 
                        success=False, error=error_msg
                    )
                return False, error_msg
    
    def list_quarantined_files(self):
        """List all quarantined files."""
        quarantined_files = []
        
        try:
            for metadata_file in self.metadata_dir.glob("*.json"):
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                    quarantined_files.append(metadata)
        except Exception as e:
            if self.logger:
                self.logger.log_app_event("error", f"Error listing quarantined files: {e}")
        
        return quarantined_files
    
    def cleanup_old_files(self):
        """Remove old quarantined files based on retention policy."""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        deleted_count = 0
        
        try:
            for metadata_file in self.metadata_dir.glob("*.json"):
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                quarantine_date = datetime.fromisoformat(metadata["quarantine_date"])
                
                if quarantine_date < cutoff_date:
                    success, _ = self.delete_quarantined_file(metadata["quarantine_id"])
                    if success:
                        deleted_count += 1
            
            if self.logger and deleted_count > 0:
                self.logger.log_app_event(
                    "info", f"Cleaned up {deleted_count} old quarantined files"
                )
        
        except Exception as e:
            if self.logger:
                self.logger.log_app_event("error", f"Error during cleanup: {e}")
        
        return deleted_count
    
    def get_quarantine_stats(self):
        """Get quarantine statistics."""
        stats = {
            "total_files": 0,
            "total_size": 0,
            "by_threat_type": {},
            "by_status": {"quarantined": 0, "restored": 0}
        }
        
        try:
            for metadata_file in self.metadata_dir.glob("*.json"):
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                stats["total_files"] += 1
                stats["total_size"] += metadata.get("original_size", 0)
                
                # Count by threat type
                threat_type = metadata.get("threat_info", {}).get("threat_type", "unknown")
                stats["by_threat_type"][threat_type] = stats["by_threat_type"].get(threat_type, 0) + 1
                
                # Count by status
                status = metadata.get("status", "quarantined")
                stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
        
        except Exception as e:
            if self.logger:
                self.logger.log_app_event("error", f"Error getting quarantine stats: {e}")
        
        return stats
    
    def _calculate_hash(self, content):
        """Calculate SHA256 hash of content."""
        import hashlib
        return hashlib.sha256(content).hexdigest()
