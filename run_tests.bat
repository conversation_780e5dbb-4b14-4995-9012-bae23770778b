@echo off
echo ========================================
echo Antivirus Endpoint Agent Test Suite
echo ========================================

echo.
echo Installing dependencies...
pip install -r requirements.txt

echo.
echo Running comprehensive tests...
python test_antivirus.py

echo.
echo Testing CLI interface...
echo.

echo Testing status command...
python main.py --status

echo.
echo Testing database stats...
python main.py --db-stats

echo.
echo Testing quarantine stats...
python main.py --quarantine-stats

echo.
echo Creating test malware file...
echo CreateRemoteThread > test_malware.txt
echo VirtualAllocEx >> test_malware.txt

echo.
echo Scanning test file...
python main.py --scan-file test_malware.txt

echo.
echo Cleaning up test file...
del test_malware.txt 2>nul

echo.
echo ========================================
echo Test suite completed!
echo ========================================
pause
